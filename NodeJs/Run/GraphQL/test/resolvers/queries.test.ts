import gql from 'graphql-tag'
import assert from 'assert'
import {
    comment,
    community,
    fullSubscription,
    limitedSubscription,
    message,
    messageThread,
    notification,
    post,
    subscribeRequest,
    testApolloServer,
    testContext,
    user,
    userDetails,
} from './test-utils'
import {
    FeaturedCategories,
    PostSortFields,
    SortDirection,
    SubscriptionOrderBy,
} from '../../src/generated/resolvers-types'
import { GraphQLResponse } from '@apollo/server'
import { PostFilterType } from '../../src/generated/api'

test('query: notificationSettings', async () => {
    const query = gql`
        query {
            notificationSettings {
                emailNewDm
                emailNewPost
                newsletter
                termsChanged
                pushNewComment
                pushNewPost
            }
        }
    `

    const context = testContext({ userId: 'user-id' })
    const notificationSettings = {
        emailNewDm: true,
        emailNewPost: false,
        newsletter: true,
        termsChanged: false,
        pushNewPost: true,
        pushNewComment: false,
    }

    context.dataSources.notificationSettingsAPI.getNotificationSettings = jest.fn(async () => notificationSettings)
    const response = await testApolloServer.executeOperation({ query }, { contextValue: context })

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult.data).toMatchSnapshot()
    expect(response.http.status).not.toBe(400)

    expect(context.dataSources.notificationSettingsAPI.getNotificationSettings).toHaveBeenCalledWith()
})

describe('query: community', () => {
    test('find by communityId', async () => {
        const query = gql`
            query {
                community(communityId: "community-id") {
                    id
                    name
                    description
                    slug
                    owner {
                        id
                        name
                    }
                    membersCount
                    image {
                        url
                        width
                        height
                        hidden
                    }
                    isVerified
                    isMember
                    threadsCount
                }
            }
        `

        const context = testContext()
        const testCommunity = community({ id: 'community-id' })
        context.dataSources.communityAPI.getCommunity = jest.fn(async () => testCommunity)

        const response = await testApolloServer.executeOperation({ query }, { contextValue: context })

        assert(response.body.kind === 'single')
        expect(response.body.singleResult.errors).toBeUndefined()
        expect(response.body.singleResult.data).toMatchSnapshot()
        expect(response.http.status).not.toBe(400)

        expect(context.dataSources.communityAPI.getCommunity).toHaveBeenCalledWith({ communityId: 'community-id' })
    })

    test('find by slug', async () => {
        const query = gql`
            query {
                community(slug: "slug") {
                    id
                    name
                    description
                    slug
                    owner {
                        id
                        name
                    }
                    membersCount
                    image {
                        url
                        width
                        height
                        hidden
                    }
                    isVerified
                    isMember
                    threadsCount
                }
            }
        `

        const context = testContext()
        const testCommunity = community({ id: 'community-id' })
        context.dataSources.communityAPI.getCommunity = jest.fn(async () => testCommunity)

        const response = await testApolloServer.executeOperation({ query }, { contextValue: context })

        assert(response.body.kind === 'single')
        expect(response.body.singleResult.errors).toBeUndefined()
        expect(response.body.singleResult.data).toMatchSnapshot()
        expect(response.http.status).not.toBe(400)

        expect(context.dataSources.communityAPI.getCommunity).toHaveBeenCalledWith({ slug: 'slug' })
    })
})

test('query: notifications', async () => {
    const query = gql`
        query {
            notifications(first: 2, filter: { categories: [] }) {
                nodes {
                    id
                    actorCount
                    createdAt
                    seenAt
                    checkedAt
                }
                pageInfo {
                    hasNextPage
                    startCursor
                    endCursor
                }
            }
        }
    `

    const context = testContext({})
    const notifications = {
        notifications: [notification({ id: 'notification-id-1' })],
        pagination: {
            hasNextPage: false,
        },
    }
    context.dataSources.notificationAPI.getNotifications = jest.fn(async () => notifications)
    const response = await testApolloServer.executeOperation({ query }, { contextValue: context })

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult.data).toMatchSnapshot()
    expect(response.http.status).not.toBe(400)

    expect(context.dataSources.notificationAPI.getNotifications).toHaveBeenCalledWith({ first: 2 }, { categories: [] })
})

test('query: subscribeRequests', async () => {
    const query = gql`
        query {
            subscribeRequests(first: 2, after: "after-cursor") {
                nodes {
                    id
                    createdAt
                }
                pageInfo {
                    hasNextPage
                    startCursor
                    endCursor
                }
            }
        }
    `

    const context = testContext({})
    const subscribeRequests = {
        subscribeRequests: [subscribeRequest({ id: 123 })],
        pagination: {
            hasNextPage: false,
        },
    }
    context.dataSources.subscribeRequestAPI.getSubscribeRequests = jest.fn(async () => subscribeRequests)
    const response = await testApolloServer.executeOperation({ query }, { contextValue: context })

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult.data).toMatchSnapshot()
    expect(response.http.status).not.toBe(400)

    expect(context.dataSources.subscribeRequestAPI.getSubscribeRequests).toHaveBeenCalledWith({
        first: 2,
        after: 'after-cursor',
    })
})

test('query: messageThread', async () => {
    const query = gql`
        query {
            messageThread(messageThreadId: "message-thread-id") {
                id
                participants {
                    name
                    bio
                }
                createdAt
                seenAt
                checkedAt
                canMessage
                lastMessage {
                    id
                    text
                    assets {
                        ... on PostImageAsset {
                            height
                            width
                            url
                        }
                        ... on PostGjirafaAsset {
                            id
                            videoStreamUrl
                            audioByteSize
                            audioStreamUrl
                            audioStaticUrl
                            hasAudio
                            hasVideo
                        }
                        ... on PostDocumentAsset {
                            name
                            documentUrl: url
                            type
                            thumbnailUrl
                        }
                        ... on MessageLockedAsset {
                            price
                        }
                    }
                }
            }
        }
    `

    const context = testContext()
    const expectedMessageThread = messageThread('message-thread-id')
    context.dataSources.messageThreadAPI.getMessageThread = jest.fn(async () => expectedMessageThread)

    const response = await testApolloServer.executeOperation({ query }, { contextValue: context })

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult.data).toMatchSnapshot()
    expect(response.http.status).not.toBe(400)

    expect(context.dataSources.messageThreadAPI.getMessageThread).toHaveBeenCalledWith('message-thread-id')
})

test('query: messageThreads', async () => {
    const query = gql`
        query {
            messageThreads(first: 10, after: "afterCursor") {
                nodes {
                    id
                    participants {
                        name
                        bio
                    }
                    createdAt
                    seenAt
                    checkedAt
                    lastMessage {
                        id
                        text
                        assets {
                            ... on PostImageAsset {
                                height
                                width
                                url
                            }
                            ... on PostGjirafaAsset {
                                id
                                videoStreamUrl
                                audioByteSize
                                audioStreamUrl
                                audioStaticUrl
                                hasAudio
                                hasVideo
                            }
                            ... on PostDocumentAsset {
                                name
                                documentUrl: url
                                type
                                thumbnailUrl
                            }
                            ... on MessageLockedAsset {
                                price
                            }
                        }
                    }
                }
                pageInfo {
                    hasNextPage
                    startCursor
                    endCursor
                }
            }
        }
    `

    const context = testContext()
    const messageThreads = {
        messageThreads: [messageThread('1683720731840-1319805956')],
        pagination: {
            endCursor: 'endCursor',
            hasNextPage: false,
        },
    }
    context.dataSources.messageThreadAPI.getMessageThreads = jest.fn(async () => messageThreads)

    const response = await testApolloServer.executeOperation({ query }, { contextValue: context })

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult.data).toMatchSnapshot()
    expect(response.http.status).not.toBe(400)

    expect(context.dataSources.messageThreadAPI.getMessageThreads).toHaveBeenCalledWith({
        first: 10,
        after: 'afterCursor',
    })
})

test('query: messages', async () => {
    const query = gql`
        query {
            messages(messageThreadId: "message-thread-id", first: 5, after: "messages-after-cursor") {
                nodes {
                    id
                    sentAt
                    text
                    price
                    assets {
                        __typename
                    }
                    sentBy {
                        id
                        name
                        subscribable
                        image {
                            url
                        }
                    }
                }
                pageInfo {
                    hasNextPage
                    endCursor
                }
            }
        }
    `
    const context = testContext()
    const messages = {
        messages: [message('message-id')],
        pagination: {
            endCursor: 'endCursor',
            hasNextPage: false,
        },
    }
    context.dataSources.messageThreadAPI.getMessages = jest.fn(async () => messages)
    context.dataSources.userAPI.getUser = jest.fn(async () => user('user-id'))

    const response = await testApolloServer.executeOperation({ query }, { contextValue: context })

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult.data).toMatchSnapshot()
    expect(response.http.status).not.toBe(400)

    expect(context.dataSources.messageThreadAPI.getMessages).toHaveBeenCalledWith('message-thread-id', {
        first: 5,
        after: 'messages-after-cursor',
    })
    expect(context.dataSources.userAPI.getUser).toHaveBeenCalledWith('sender-id')
})

test('query: livestreams', async () => {
    const query = gql`
        query {
            livestreams {
                nodes {
                    __typename
                    id
                    counts {
                        comments
                        replies
                    }
                    pinnedAt
                    publishedAt
                    state
                    ... on CompleteContentPost {
                        text
                    }
                    categories {
                        id
                        name
                        slug
                    }
                }
                pageInfo {
                    hasNextPage
                    endCursor
                }
            }
        }
    `
    const context = testContext({ userId: 'user-id' })
    const posts = {
        posts: [post({ id: 'post-id', fullAssets: true })],
        pagination: {
            endCursor: 'endCursor',
            hasNextPage: false,
        },
    }
    context.dataSources.postAPI.getLivestreams = jest.fn(async () => posts)

    const response = await testApolloServer.executeOperation({ query }, { contextValue: context })

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult.data).toMatchSnapshot()
    expect(response.http.status).not.toBe(400)

    expect(context.dataSources.postAPI.getLivestreams).toHaveBeenCalledWith('user-id')
})

test('query: posts', async () => {
    const query = gql`
        query {
            posts(
                id: "creator-id"
                first: 3
                after: "posts-after-cursor"
                filter: {
                    categoryId: "estermalenovappwftsvc"
                    creatorId: "creator-id"
                    type: IN_PROGRESS
                    communityId: "20ff3aba-123c-4c4d-8b2c-f193a62a0df7"
                    excludedCreatorIds: ["excluded-creator-id"]
                }
                sort: { by: PINNED_AT, order: ASC }
            ) {
                nodes {
                    __typename
                    id
                    counts {
                        comments
                        replies
                    }
                    pinnedAt
                    publishedAt
                    state
                    ... on CompleteContentPost {
                        text
                    }
                    categories {
                        id
                        name
                        slug
                    }
                }
                pageInfo {
                    hasNextPage
                    endCursor
                }
            }
        }
    `
    const context = testContext()
    const posts = {
        posts: [post({ id: 'post-id', fullAssets: true })],
        pagination: {
            endCursor: 'endCursor',
            hasNextPage: false,
        },
    }
    context.dataSources.postAPI.getPosts = jest.fn(async () => posts)

    const response = await testApolloServer.executeOperation({ query }, { contextValue: context })

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult.data).toMatchSnapshot()
    expect(response.http.status).not.toBe(400)

    expect(context.dataSources.postAPI.getPosts).toHaveBeenCalledWith(
        'creator-id',
        {
            first: 3,
            after: 'posts-after-cursor',
        },
        {
            by: PostSortFields.PINNED_AT,
            order: SortDirection.ASC,
        },
        {
            categoryId: 'estermalenovappwftsvc',
            creatorId: 'creator-id',
            type: PostFilterType.IN_PROGRESS,
            communityId: '20ff3aba-123c-4c4d-8b2c-f193a62a0df7',
            excludedCreatorIds: ['excluded-creator-id'],
        }
    )
})

test('query: post', async () => {
    const query = gql`
        query {
            post(id: "post-id") {
                __typename
                id
                counts {
                    comments
                    replies
                }
                pinnedAt
                publishedAt
                state
                ... on CompleteContentPost {
                    isSponsored
                    isAgeRestricted
                    text
                    textHtml
                    markdown
                    savedPostInfo {
                        savedAt
                    }
                    comments(first: 1) {
                        nodes {
                            id
                            text
                        }
                    }
                }
            }
        }
    `
    const context = testContext()
    const comments = {
        comments: [comment({ id: 'comment-id', fullAssets: true })],
        pagination: {
            endCursor: 'endCursor',
            hasNextPage: false,
        },
    }
    context.dataSources.postAPI.getPost = jest.fn(async () =>
        post({
            id: 'post-id',
            fullAssets: true,
            savedPostInfo: true,
            textHtml: '<strong>text</strong>',
        })
    )
    context.dataSources.postAPI.getComments = jest.fn(async () => comments)

    const response = await testApolloServer.executeOperation({ query }, { contextValue: context })

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult.data).toMatchSnapshot()
    expect(response.http.status).not.toBe(400)

    expect(context.dataSources.postAPI.getPost).toHaveBeenCalledWith('post-id')
    expect(context.dataSources.postAPI.getComments).toHaveBeenCalledWith('post-id', { first: 1 })
})

test('query: comments', async () => {
    const query = gql`
        query {
            comments(id: "post-id", first: 4, after: "comments-after-cursor", sortDirection: ASC) {
                nodes {
                    __typename
                    id
                    counts {
                        comments
                        replies
                    }
                    publishedAt
                    state
                    textMarkdown
                    user {
                        id
                        name
                    }
                }
                pageInfo {
                    hasNextPage
                    endCursor
                }
            }
        }
    `
    const context = testContext()
    const comments = {
        comments: [comment({ id: 'comment-id', userId: 'user-id', fullAssets: true, text: 'text' })],
        pagination: {
            endCursor: 'endCursor',
            hasNextPage: false,
        },
    }
    context.dataSources.postAPI.getComments = jest.fn(async () => comments)
    context.dataSources.userAPI.getUser = jest.fn(async (userId: string) => user(userId))

    const response = await testApolloServer.executeOperation({ query }, { contextValue: context })

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult.data).toMatchSnapshot()
    expect(response.http.status).not.toBe(400)

    expect(context.dataSources.postAPI.getComments).toHaveBeenCalledWith('post-id', {
        first: 4,
        after: 'comments-after-cursor',
        sortDirection: SortDirection.ASC,
    })
    expect(context.dataSources.userAPI.getUser).toHaveBeenCalledWith('user-id')
})

test('query: comment', async () => {
    const query = gql`
        query {
            comment(id: "comment-id") {
                __typename
                id
                counts {
                    comments
                    replies
                }
                publishedAt
                state
                user {
                    id
                    name
                }
            }
        }
    `

    const context = prepareAndMockContext()
    const response = await testApolloServer.executeOperation({ query }, { contextValue: context.contextValue })

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult.data).toMatchSnapshot()
    expect(response.http.status).not.toBe(400)

    expect(context.mockGetComment).toHaveBeenCalledWith('comment-id')

    function prepareAndMockContext() {
        const contextValue = testContext()

        const mockGetComment = jest.fn(async () => comment())
        contextValue.dataSources.postAPI.getComment = mockGetComment

        return {
            contextValue,
            mockGetComment,
        }
    }
})

test('query: user', async () => {
    const query = gql`
        query {
            user(id: "user-id") {
                id
                name
                bio
                bioMarkdown
                counts {
                    posts
                    supporters
                    supporting
                }
                categories {
                    name
                }
                tier {
                    id
                    currency
                    priceCents
                }
                verified
                subscription {
                    id
                    subscriber {
                        id
                        name
                    }
                    creator {
                        id
                        name
                    }
                }
                privacyPolicyEnabled
                analytics {
                    facebookPixelId
                }
                hasGiftsAllowed
                emailPublic
                profileType
            }
        }
    `
    const context = testContext({ userId: 'requester-id' })
    context.dataSources.subscriptionAPI.getSubscription = jest.fn(async () => fullSubscription())
    context.dataSources.userAPI.getUser = jest.fn(async (userId: string) => user(userId))

    const response = await testApolloServer.executeOperation({ query }, { contextValue: context })

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult.data).toMatchSnapshot()
    expect(response.http.status).not.toBe(400)

    expect(context.dataSources.userAPI.getUser).toHaveBeenCalledWith('user-id')
    expect(context.dataSources.subscriptionAPI.getSubscription).toHaveBeenCalledWith('requester-id', 'user-id')
})

test('query: viewer', async () => {
    const query = gql`
        query {
            viewer {
                id
                name
                counts {
                    supporters
                    supporting
                    posts
                    incomes
                    incomesClean
                    payments
                    invoices
                }
                bio
                bioMarkdown
                path
                image {
                    url
                }
                hasRssFeed
                discord {
                    id
                }
                livestream {
                    streamKey
                    streamUrl
                    playbackUrl
                }
                role
                isOfAge
                emailPublic
                emailInvoice
            }
        }
    `
    const context = testContext({ userId: 'requester' })
    context.dataSources.userAPI.getUserDetails = jest.fn(async (userId: string) => userDetails({ id: userId }))
    context.dataSources.gjirafaAPI.getPlaybackUrl = jest.fn(async () => 'playback-url')

    const response = await testApolloServer.executeOperation({ query }, { contextValue: context })

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult.data).toMatchSnapshot()
    expect(response.http.status).not.toBe(400)

    expect(context.dataSources.userAPI.getUserDetails).toHaveBeenCalledWith('requester')
    expect(context.dataSources.gjirafaAPI.getPlaybackUrl).toHaveBeenCalledWith('public-id')
})

test('query: adminUserDetails', async () => {
    const query = gql`
        query {
            adminUserDetails(id: "user-id") {
                id
                name
                counts {
                    supporters
                    supporting
                    posts
                    incomes
                    incomesClean
                    payments
                    invoices
                }
                bio
                path
                image {
                    url
                }
                hasRssFeed
                discord {
                    id
                }
                livestream {
                    streamKey
                    streamUrl
                }
                role
            }
        }
    `
    const context = testContext({ userId: 'requester' })
    context.dataSources.userAPI.getUserDetailsAsAdmin = jest.fn(async (userId: string) => userDetails({ id: userId }))

    const response = await testApolloServer.executeOperation({ query }, { contextValue: context })

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult.data).toMatchSnapshot()
    expect(response.http.status).not.toBe(400)

    expect(context.dataSources.userAPI.getUserDetailsAsAdmin).toHaveBeenCalledWith('user-id')
})

test('query: searchUsers', async () => {
    const query = gql`
        query {
            searchUsers(query: "query-string", first: 10) {
                nodes {
                    id
                    name
                    counts {
                        posts
                        supporters
                        supporting
                    }
                    categories {
                        name
                    }
                    tier {
                        id
                        priceCents
                        currency
                    }
                    verified
                }
            }
        }
    `
    const context = testContext()
    context.dataSources.userAPI.searchUsersDeprecated = jest.fn(async () => [user('user-id')])

    const response = await testApolloServer.executeOperation({ query }, { contextValue: context })

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult.data).toMatchSnapshot()
    expect(response.http.status).not.toBe(400)

    expect(context.dataSources.userAPI.searchUsersDeprecated).toHaveBeenCalledWith('query-string', { first: 10 })
})

describe('query: featuredCreators', () => {
    test('locale is not passed, users details are fetched', async () => {
        const query = gql`
            query {
                featuredCreators(first: 11) {
                    nodes {
                        id
                        name
                        counts {
                            posts
                            supporters
                            supporting
                        }
                        categories {
                            name
                        }
                        tier {
                            id
                            priceCents
                            currency
                        }
                        verified
                    }
                }
            }
        `
        const context = testContext({ userId: 'viewer-id' })
        context.dataSources.userAPI.featuredUsers = jest.fn(async () => [user('user-id')])
        context.dataSources.userAPI.getUserDetails = jest.fn(async () =>
            userDetails({
                id: 'viewer-id',
                language: 'en',
            })
        )

        const response = await testApolloServer.executeOperation({ query }, { contextValue: context })

        assert(response.body.kind === 'single')
        expect(response.body.singleResult.errors).toBeUndefined()
        expect(response.body.singleResult.data).toMatchSnapshot()
        expect(response.http.status).not.toBe(400)

        expect(context.dataSources.userAPI.featuredUsers).toHaveBeenCalledWith('en', { first: 11 })
        expect(context.dataSources.userAPI.getUserDetails).toHaveBeenCalledWith('viewer-id')
    })

    test('locale is passed in, user api is not called', async () => {
        const query = gql`
            query {
                featuredCreators(locale: "fr", first: 11) {
                    nodes {
                        id
                        name
                        counts {
                            posts
                            supporters
                            supporting
                        }
                        categories {
                            name
                        }
                        tier {
                            id
                            priceCents
                            currency
                        }
                        verified
                    }
                }
            }
        `
        const context = testContext()
        context.dataSources.userAPI.featuredUsers = jest.fn(async () => [user('user-id')])
        context.dataSources.userAPI.getUserDetails = jest.fn(async () => userDetails({ id: 'viewer-id' }))

        const response = await testApolloServer.executeOperation({ query }, { contextValue: context })

        assert(response.body.kind === 'single')
        expect(response.body.singleResult.errors).toBeUndefined()
        expect(response.body.singleResult.data).toMatchSnapshot()
        expect(response.http.status).not.toBe(400)

        expect(context.dataSources.userAPI.featuredUsers).toHaveBeenCalledWith('fr', { first: 11 })
        expect(context.dataSources.userAPI.getUserDetails).not.toHaveBeenCalled()
    })
})

test('query: featuredCreatorsRandomized', async () => {
    const query = gql`
        query {
            featuredCreatorsRandomized(featuredCategory: NEW_CREATORS) {
                users {
                    name
                }
            }
        }
    `
    const context = testContext({ userId: 'viewer-id' })
    context.dataSources.userAPI.featuredCreatorsRandomized = jest.fn(async () => [user('user-id')])

    const response = await testApolloServer.executeOperation({ query }, { contextValue: context })

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult.data).toMatchSnapshot()
    expect(response.http.status).not.toBe(400)

    expect(context.dataSources.userAPI.featuredCreatorsRandomized).toHaveBeenCalledWith(FeaturedCategories.NEW_CREATORS)
})

describe('query: subscribers', () => {
    test('full access on subscribers', async () => {
        const query = gql`
            query {
                subscribers(creatorId: "creator-id", orderBy: GIFTED_FIRST, first: 10) {
                    nodes {
                        id
                        subscribedAt
                        subscriber {
                            id
                            name
                        }
                        creator {
                            id
                            name
                        }
                        ... on UserSubscriptionDetails {
                            couponAppliedForMonths
                            couponMethod
                            couponPercentOff
                            type
                            tier {
                                id
                                priceCents
                                currency
                            }
                            status
                        }
                    }
                    pageInfo {
                        hasNextPage
                        startCursor
                        endCursor
                    }
                }
            }
        `
        const context = testContext({ userId: 'creator-id' })
        const subscriptions = {
            subscriptions: [fullSubscription()],
            pagination: {
                endCursor: 'endCursor',
                hasNextPage: false,
            },
        }
        context.dataSources.subscriptionAPI.getSubscribers = jest.fn(async () => subscriptions)

        const response = await testApolloServer.executeOperation({ query }, { contextValue: context })

        assert(response.body.kind === 'single')
        expect(response.body.singleResult.errors).toBeUndefined()
        expect(response.body.singleResult.data).toMatchSnapshot()
        expect(response.http.status).not.toBe(400)

        expect(context.dataSources.subscriptionAPI.getSubscribers).toHaveBeenCalledWith(
            'creator-id',
            SubscriptionOrderBy.GIFTED_FIRST,
            {
                first: 10,
            }
        )
    })

    test('limited access on subscribers', async () => {
        const query = gql`
            query {
                subscribers(creatorId: "creator-id", orderBy: GIFTED_FIRST, first: 10) {
                    nodes {
                        id
                        subscribedAt
                        subscriber {
                            id
                            name
                        }
                        creator {
                            id
                            name
                        }
                        ... on UserSubscriptionDetails {
                            couponAppliedForMonths
                            couponMethod
                            couponPercentOff
                            type
                            tier {
                                id
                                priceCents
                                currency
                            }
                            status
                        }
                    }
                    pageInfo {
                        hasNextPage
                        startCursor
                        endCursor
                    }
                }
            }
        `
        const context = testContext({ userId: 'another-id' })
        const subscriptions = {
            subscriptions: [limitedSubscription()],
            pagination: {
                endCursor: 'endCursor',
                hasNextPage: false,
            },
        }
        context.dataSources.subscriptionAPI.getSubscribers = jest.fn(async () => subscriptions)

        const response = await testApolloServer.executeOperation({ query }, { contextValue: context })

        assert(response.body.kind === 'single')
        expect(response.body.singleResult.errors).toBeUndefined()
        expect(response.body.singleResult.data).toMatchSnapshot()
        expect(response.http.status).not.toBe(400)

        expect(context.dataSources.subscriptionAPI.getSubscribers).toHaveBeenCalledWith(
            'creator-id',
            SubscriptionOrderBy.GIFTED_FIRST,
            {
                first: 10,
            }
        )
    })
})

describe('query: subscriptions', () => {
    test('full access on subscriptions', async () => {
        const query = gql`
            query {
                subscriptions(userId: "user-id", orderBy: OLDEST, first: 5, after: "cursor") {
                    nodes {
                        id
                        subscribedAt
                        subscriber {
                            id
                            name
                        }
                        creator {
                            id
                            name
                        }
                        ... on UserSubscriptionDetails {
                            couponAppliedForMonths
                            couponMethod
                            couponPercentOff
                            type
                            tier {
                                id
                                priceCents
                                currency
                            }
                            status
                        }
                    }
                    pageInfo {
                        hasNextPage
                        startCursor
                        endCursor
                    }
                }
            }
        `
        const context = testContext({ userId: 'user-id' })
        const subscriptions = {
            subscriptions: [fullSubscription()],
            pagination: {
                endCursor: 'endCursor',
                hasNextPage: false,
            },
        }
        context.dataSources.subscriptionAPI.getSubscriptions = jest.fn(async () => subscriptions)

        const response = await testApolloServer.executeOperation({ query }, { contextValue: context })

        assert(response.body.kind === 'single')
        expect(response.body.singleResult.errors).toBeUndefined()
        expect(response.body.singleResult.data).toMatchSnapshot()
        expect(response.http.status).not.toBe(400)

        expect(context.dataSources.subscriptionAPI.getSubscriptions).toHaveBeenCalledWith(
            'user-id',
            SubscriptionOrderBy.OLDEST,
            {
                first: 5,
                after: 'cursor',
            },
            undefined
        )
    })

    test('limited access on subscriptions', async () => {
        const query = gql`
            query {
                subscriptions(userId: "user-id", orderBy: OLDEST, first: 5, after: "cursor") {
                    nodes {
                        id
                        subscribedAt
                        subscriber {
                            id
                            name
                        }
                        creator {
                            id
                            name
                        }
                        ... on UserSubscriptionDetails {
                            couponAppliedForMonths
                            couponMethod
                            couponPercentOff
                            type
                            tier {
                                id
                                priceCents
                                currency
                            }
                            status
                        }
                    }
                    pageInfo {
                        hasNextPage
                        startCursor
                        endCursor
                    }
                }
            }
        `
        const context = testContext({ userId: 'another-id' })
        const subscriptions = {
            subscriptions: [limitedSubscription()],
            pagination: {
                endCursor: 'endCursor',
                hasNextPage: false,
            },
        }
        context.dataSources.subscriptionAPI.getSubscriptions = jest.fn(async () => subscriptions)

        const response = await testApolloServer.executeOperation({ query }, { contextValue: context })

        assert(response.body.kind === 'single')
        expect(response.body.singleResult.errors).toBeUndefined()
        expect(response.body.singleResult.data).toMatchSnapshot()
        expect(response.http.status).not.toBe(400)

        expect(context.dataSources.subscriptionAPI.getSubscriptions).toHaveBeenCalledWith(
            'user-id',
            SubscriptionOrderBy.OLDEST,
            {
                first: 5,
                after: 'cursor',
            },
            undefined
        )
    })

    test('expired subscriptions', async () => {
        const query = gql`
            query {
                subscriptions(
                    userId: "user-id"
                    orderBy: OLDEST
                    first: 5
                    after: "cursor"
                    filter: { expired: true }
                ) {
                    nodes {
                        id
                    }
                }
            }
        `
        const context = testContext({ userId: 'user-id' })
        const subscriptions = {
            subscriptions: [fullSubscription()],
            pagination: {
                endCursor: 'endCursor',
                hasNextPage: false,
            },
        }
        context.dataSources.subscriptionAPI.getSubscriptions = jest.fn(async () => subscriptions)

        const response = await testApolloServer.executeOperation({ query }, { contextValue: context })

        assert(response.body.kind === 'single')
        expect(response.body.singleResult.errors).toBeUndefined()
        expect(response.body.singleResult.data).toMatchSnapshot()
        expect(response.http.status).not.toBe(400)

        expect(context.dataSources.subscriptionAPI.getSubscriptions).toHaveBeenCalledWith(
            'user-id',
            SubscriptionOrderBy.OLDEST,
            {
                first: 5,
                after: 'cursor',
            },
            {
                expired: true,
            }
        )
    })
})

test('query: savedPosts', async () => {
    const query = gql`
        query {
            savedPosts(first: 10, after: "afterCursor", filter: { subscribedCreatorsOnly: true }) {
                nodes {
                    savedAt
                    post {
                        id
                        state
                        pinnedAt
                        publishedAt
                    }
                }
            }
        }
    `
    const context = testContext()
    const savedPosts = {
        savedPosts: [
            {
                id: 'saved-post-id',
                savedAt: '2023-05-10T12:12:11.844280Z',
                post: post({ id: 'post-id' }),
            },
        ],
        pagination: {
            endCursor: 'endCursor',
            hasNextPage: false,
        },
    }
    context.dataSources.libraryAPI.getSavedPosts = jest.fn(async () => savedPosts)

    const response = await testApolloServer.executeOperation({ query }, { contextValue: context })

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult.data).toMatchSnapshot()
    expect(response.http.status).not.toBe(400)

    expect(context.dataSources.libraryAPI.getSavedPosts).toHaveBeenCalledWith({ first: 10, after: 'afterCursor' }, true)
})

test('max depth of a query is 7', async () => {
    const query = gql`
        query {
            post(id: "") {
                ... on CompleteContentPost {
                    comments(first: 1) {
                        nodes {
                            parent {
                                ... on CompleteContentPost {
                                    comments(first: 1) {
                                        nodes {
                                            parent {
                                                ... on CompleteContentPost {
                                                    id
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    `

    const response = await testApolloServer.executeOperation({ query }, { contextValue: testContext() })
    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toEqual([
        {
            extensions: {
                code: 'GRAPHQL_VALIDATION_FAILED',
            },
            message: 'Syntax Error: Query depth limit of 7 exceeded, found 8.',
        },
    ])
})

test('query: gjirafaVideoQualities', async () => {
    const query = gql`
        query {
            gjirafaVideoQualities(assetId: "vjsnqrol") {
                duration
                quality
                size
            }
        }
    `
    const context = testContext()
    context.dataSources.gjirafaAPI.getVideoQualities = jest.fn(async () => {
        return [
            {
                quality: '2160p',
                size: 3512201772,
                duration: 1119.914667,
            },
        ]
    })

    const response = await testApolloServer.executeOperation({ query }, { contextValue: context })

    assertResponse(response)
    expect(context.dataSources.gjirafaAPI.getVideoQualities).toHaveBeenCalledWith('vjsnqrol')
})

test('query: expectedIncome', async () => {
    const query = gql`
        query {
            expectedIncome {
                grossIncomeCents
                netIncomeCents
            }
        }
    `
    const context = testContext({ userId: 'ffmxcvpkjysib' })
    context.dataSources.statisticsAPI.getExpectedIncome = jest.fn(async () => ({
        grossIncomeCents: 932,
        netIncomeCents: 820,
    }))

    const response = await testApolloServer.executeOperation({ query }, { contextValue: context })

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult.data).toMatchSnapshot()
    expect(response.http.status).not.toBe(400)

    expect(context.dataSources.statisticsAPI.getExpectedIncome).toHaveBeenCalledWith('ffmxcvpkjysib')
})

function assertResponse(response: GraphQLResponse) {
    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult.data).toMatchSnapshot()
    expect(response.http.status).not.toBe(400)
}
