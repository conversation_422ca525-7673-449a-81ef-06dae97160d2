import { DataSourceConfig } from '@apollo/datasource-rest'
import { NotificationResponse, PagedNotificationResponse } from '../generated/api'
import { Environment } from '../common/environment'
import { ServiceDataSource } from './ServiceDataSource'
import { NotificationModel } from '../models/notification'
import { PaginationModel } from '../models/pagination'
import { mapNotificationType, mapToUser } from './common-mappers'
import { PaginationParams, paginationParamsToQueryParams } from './common-utils'
import { NotificationTypeCategory } from '../generated/resolvers-types'

export class NotificationAPI extends ServiceDataSource {
    constructor(environment: Environment, cookies?: string, config?: DataSourceConfig) {
        super(environment, 'api', cookies, config)
    }

    async getNotifications(
        paginationParams: PaginationParams,
        filter?: {
            categories?: NotificationTypeCategory[]
        }
    ): Promise<{ notifications: NotificationModel[]; pagination: PaginationModel }> {
        const params = paginationParamsToQueryParams(paginationParams)
        filter?.categories?.forEach((category) => {
            params.append('categories', category)
        })

        const response = await this.get<PagedNotificationResponse>(`/v1/notifications`, {
            params,
        })
        const mappedNotifications = response.content.map((notification) => {
            return {
                id: notification.id,
                seenAt: notification.seenAt,
                actorCount: notification.actorCount,
                createdAt: notification.createdAt,
                checkedAt: notification.checkedAt,
                type: mapNotificationType(notification.type),
                objectId: notification.relationships.objectId,
                objectType: notification.relationships.objectType,
                lastActorId: notification.relationships.lastActorId,
                lastActor: notification.lastActor ? mapToUser(notification.lastActor) : undefined,
                communityId: notification.communityId,
            }
        })

        return {
            notifications: mappedNotifications,
            pagination: {
                endCursor: response.afterCursor,
                startCursor: response.beforeCursor,
                hasNextPage: response.hasNext,
            },
        }
    }

    async updateNotification(
        id: string,
        checkedAt: Date | string | undefined | null,
        seenAt: Date | string | undefined | null
    ): Promise<NotificationModel> {
        const response = await this.put<NotificationResponse>(`/v1/notifications/${id}`, {
            body: {
                checkedAt,
                seenAt,
            },
        })

        return {
            id: response.id,
            type: mapNotificationType(response.type),
            createdAt: response.createdAt,
            checkedAt: response.checkedAt,
            seenAt: response.seenAt,
            objectId: response.relationships.objectId,
            objectType: response.relationships.objectType,
            lastActorId: response.relationships.lastActorId,
            actorCount: response.actorCount,
            communityId: response.communityId,
        }
    }

    async markAllSeen(userId: string) {
        await this.post(`/v1/users/${userId}/mark-notifications-seen`)
    }
}
