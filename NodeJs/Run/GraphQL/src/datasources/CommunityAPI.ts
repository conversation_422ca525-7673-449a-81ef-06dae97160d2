import { ServiceDataSource } from './ServiceDataSource'
import { Environment } from '../common/environment'
import { DataSourceConfig } from '@apollo/datasource-rest'
import { CommunityModel } from '../models/community'
import { CommunityResponse, UpdateCommunityRequest } from '../generated/api'
import { mapToUser } from './common-mappers'

export class CommunityAPI extends ServiceDataSource {
    constructor(environment: Environment, cookies?: string, config?: DataSourceConfig) {
        super(environment, 'api', cookies, config)
    }

    async getCommunity({
        communityId,
        slug,
    }: {
        communityId?: string | null
        slug?: string | null
    }): Promise<CommunityModel> {
        let response
        if (slug) {
            response = await this.get<CommunityResponse>(`/v1/communities/${slug}?isSlug=true`)
        } else if (communityId) {
            response = await this.get<CommunityResponse>(`/v1/communities/${communityId}`)
        } else {
            throw new Error('Either communityId or slug must be provided')
        }

        return this.mapToCommunity(response)
    }

    async postCommunities(): Promise<CommunityModel> {
        const response = await this.post<CommunityResponse>('/v1/communities')

        return this.mapToCommunity(response)
    }

    async putCommunities(communityId: string, input: UpdateCommunityRequest): Promise<CommunityModel> {
        const response = await this.put<CommunityResponse>(`/v1/communities/${communityId}`, { body: input })

        return this.mapToCommunity(response)
    }

    private mapToCommunity(response: CommunityResponse): CommunityModel {
        return {
            id: response.id,
            name: response.name,
            description: response.description,
            slug: response.slug,
            ownerId: response.ownerId,
            owner: mapToUser(response.owner),
            membersCount: response.membersCount,
            ...(response.image && {
                image: {
                    url: response.image.id,
                    height: response.image.height,
                    width: response.image.width,
                    hidden: response.image.hidden,
                },
            }),
            createdAt: response.createdAt,
            isVerified: response.isVerified,
            isMember: response.isMember,
            threadsCount: response.threadsCount,
        }
    }
}
