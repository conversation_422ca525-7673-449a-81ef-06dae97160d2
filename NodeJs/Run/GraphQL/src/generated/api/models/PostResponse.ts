/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CategoryResponse } from './CategoryResponse';
import type { Chapter } from './Chapter';
import type { PostAssetDto } from './PostAssetDto';
import type { PostCounts } from './PostCounts';
import type { PostRelationships } from './PostRelationships';
import type { PostState } from './PostState';
import type { SavedCreatorPostInfoResponse } from './SavedCreatorPostInfoResponse';
export type PostResponse = {
    id: string;
    state: PostState;
    text?: string | null;
    textHtml?: string | null;
    textDelta?: string | null;
    fullAsset?: boolean | null;
    excludeFromRss?: boolean | null;
    pinnedAt?: string | null;
    assets: Array<PostAssetDto>;
    categories: Array<CategoryResponse>;
    counts: PostCounts;
    publishedAt?: string | null;
    price?: number | null;
    assetsCount?: number | null;
    savedPostInfo?: SavedCreatorPostInfoResponse;
    relationships: PostRelationships;
    chapters: Array<Chapter>;
    isAgeRestricted: boolean;
    isSponsored: boolean;
    pollId?: string | null;
    myVote: number;
    voteScore: number;
};

