/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { NotificationRelationships } from './NotificationRelationships';
import type { NotificationType } from './NotificationType';
import type { UserResponse } from './UserResponse';
export type NotificationResponse = {
    id: string;
    type: NotificationType;
    createdAt: string;
    checkedAt?: string | null;
    seenAt?: string | null;
    actorCount: number;
    lastActor?: UserResponse;
    communityId?: string | null;
    relationships: NotificationRelationships;
};

