/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { GjirafaStatus } from './GjirafaStatus';
export type GjirafaAsset = {
    progressTillReadiness: number;
    progressTillCompleteness: number;
    width?: number | null;
    height?: number | null;
    key?: string | null;
    videoStreamUrl?: string | null;
    audioStreamUrl?: string | null;
    audioStaticUrl?: string | null;
    chaptersVttUrl?: string | null;
    hasAudio: boolean;
    hasVideo: boolean;
    id: string;
    hidden: boolean;
    duration: number;
    audioByteSize?: number | null;
    status: GjirafaStatus;
    keyId?: string | null;
    encodingRemainingTime?: number | null;
    encodingFinishTime?: string | null;
    createdAt?: string | null;
    projectId?: string | null;
    thumbnailBlurhash?: string | null;
    imageKey: string;
    drmKey?: string | null;
    encodeKey?: string | null;
    previewAnimatedUrl: string;
    previewStaticUrl: string;
    previewStripUrl: string;
};

