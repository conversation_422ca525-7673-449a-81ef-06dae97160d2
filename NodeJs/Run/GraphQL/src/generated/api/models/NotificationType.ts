/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
export enum NotificationType {
    NEW_SUBSCRIPTION = 'new_subscription',
    SUBSCRIBE_REQUEST_ACCEPTED = 'subscribe_request_accepted',
    CANCELLED_SUBSCRIPTION_ENDED = 'cancelled_subscription_ended',
    CANCELLED_SUBSCRIPTION_INSUFFICIENT_FUNDS = 'cancelled_subscription_insufficient_funds',
    CANCELLED_SUBSCRIPTION_REFUSED = 'cancelled_subscription_refused',
    CANCELLED_SUBSCRIPTION_REFUNDED = 'cancelled_subscription_refunded',
    CANCELLED_SUBSCRIPTION_BY_CREATOR = 'cancelled_subscription_by_creator',
    CANCELLED_SUBSCRIPTION_OTHER = 'cancelled_subscription_other',
    NEW_POST = 'new_post',
    NEW_LIVESTREAM = 'new_livestream',
    NEW_COMMENT = 'new_comment',
    NEW_REPLY = 'new_reply',
    NEW_REPLY_TO_REPLY = 'new_reply_to_reply',
    PAID_POST = 'paid_post',
    PAYMENT_CARD_DECLINED = 'payment_card_declined',
    PAYMENT_INSUFFICIENT_FUNDS = 'payment_insufficient_funds',
    PAYMENT_FAILED = 'payment_failed',
    NEWSLETTER = 'newsletter',
    TERMS_CHANGED = 'terms_changed',
    NEW_THREAD = 'new_thread',
}
