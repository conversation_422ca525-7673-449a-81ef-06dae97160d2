package hero

plugins {
    java
    id("hero.cloud-function-conventions")
}

val projectModule: (String) -> String by extra
val invoker: Configuration by configurations.creating
dependencies {
    implementation(projectModule(":Modules:Http4k"))
    invoker("com.google.cloud.functions.invoker:java-function-invoker:_")
}

task<JavaExec>("runFunction") {
    mainClass.set("com.google.cloud.functions.invoker.runner.Invoker")
    classpath(invoker)
    inputs.files(configurations.runtimeClasspath, sourceSets.main.get().output)
    args(
        "--target",
        project.findProperty("run.functionTarget") ?: "",
        "--port",
        project.findProperty("run.port") ?: 8080,
    )
    doFirst {
        args("--classpath", files(configurations.runtimeClasspath, sourceSets.main.get().output).asPath)
    }
}
