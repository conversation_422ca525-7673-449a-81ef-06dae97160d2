package hero

import com.github.jengelman.gradle.plugins.shadow.tasks.ShadowJar

plugins {
    java
    id("com.github.johnrengelman.shadow")
    id("hero.kotlin-conventions")
}

val projectModule: (String) -> String by extra
dependencies {
    implementation(projectModule(":Modules:BaseUtils"))
    implementation(projectModule(":Modules:GoogleCloud"))

    // this API instead of IMPLEMENTATION to force shadow jar to exclude it from minimization
    // otherwise Exclude annotation is removed, for some unknown reason though
    api(projectModule(":Modules:Model"))

    // every function needs this dependency to get the Functions Framework API
    implementation("com.google.cloud.functions:functions-framework-api:_")

    testImplementation("com.google.cloud.functions:functions-framework-api:_")
    testImplementation(projectModule(":Modules:Testing"))
}

configure<PublishingExtension> {
    publications {
        val publication = create<MavenPublication>(project.name + "-shadow") {
            groupId = "hero"
            artifactId = project.name.lowercase() + "-shadow"
            version = "ci"
        }
        project.shadow.component(publication)
    }
}

// Minimize the size of the JAR file for deployment to cloud functions
// by excluding unnecessary dependencies which are not used by the project.
// Some dependencies must be included though (they are excluded below) because
// of runtime loading for example via ServiceLoader
// just look at the corresponding MR or see https://imperceptiblethoughts.com/shadow/configuration/minimizing/
tasks.withType<ShadowJar> {
    minimize {
        exclude(dependency("com.google.firebase:firebase-admin:.*"))
        exclude(dependency("com.google.cloud:google-cloud-firestore:.*"))
        exclude(dependency("org.jetbrains.kotlin:.*"))
        exclude(dependency("org.apache.logging.log4j:.*"))
        exclude(dependency("org.postgresql:.*:.*"))
        exclude(dependency("com.google.cloud.sql:postgres-socket-factory:.*"))
        exclude(dependency("io.jsonwebtoken:.*:.*"))
    }
}
