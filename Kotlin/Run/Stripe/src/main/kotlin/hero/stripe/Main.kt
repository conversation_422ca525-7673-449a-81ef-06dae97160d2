package hero.stripe

import hero.baseutils.SystemEnv
import hero.baseutils.envPrefix
import hero.baseutils.log
import hero.baseutils.systemEnv
import hero.gcloud.PubSub
import hero.gcloud.TypedCollectionReference
import hero.gcloud.firestore
import hero.gcloud.get
import hero.gcloud.typedCollectionOf
import hero.http4k.http4kInJetty
import hero.model.Currency
import hero.model.Invoice
import hero.model.Subscriber
import hero.model.Tier
import hero.model.User
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import hero.stripe.account.controller.AccountWebhookController
import hero.stripe.account.service.AccountCommandService
import hero.stripe.charge.ChargeCommandService
import hero.stripe.charge.controller.ChargeWebhookController
import hero.stripe.coupon.controller.CouponWebhookController
import hero.stripe.coupon.service.CouponCommandService
import hero.stripe.customer.controller.CustomerWebhookController
import hero.stripe.customer.service.CustomerCommandService
import hero.stripe.fees.controller.ReportWebhookController
import hero.stripe.invoice.controller.InvoiceWebhookController
import hero.stripe.invoice.service.InvoiceCommandService
import hero.stripe.model.StripeKeys
import hero.stripe.paymentmethod.controller.PaymentMethodWebhookController
import hero.stripe.paymentmethod.service.PaymentMethodCommandService
import hero.stripe.payout.controller.PayoutWebhookController
import hero.stripe.payout.service.PayoutCommandService
import hero.stripe.service.AppleSigningService
import hero.stripe.service.AppleSubscriptionService
import hero.stripe.service.StripeClients
import hero.stripe.service.StripeCouponService
import hero.stripe.service.StripePaymentMethodsService
import hero.stripe.service.StripeService
import hero.stripe.service.StripeSubscriberSaver
import hero.stripe.service.StripeSubscriptionService
import hero.stripe.service.VatMappingProvider
import hero.stripe.subscription.controller.SubscriptionWebhookController
import hero.stripe.subscription.service.SubscriptionCommandService
import org.jooq.DSLContext

fun main() {
    log.info("Service is starting.")
    val environment = SystemEnv.environment
    val isLocalHost = systemEnv("LOG_APPENDER") != "ConsoleFluentD"
    val pubSub = PubSub(SystemEnv.environment, SystemEnv.cloudProject)
    val firestore = firestore(SystemEnv.cloudProject, SystemEnv.isProduction)
    val production = SystemEnv.isProduction
    val hostnameServices = SystemEnv.hostnameServices
    val hostname = SystemEnv.hostname

    val usersCollection = firestore.typedCollectionOf(User)
    val invoicesCollection = firestore.typedCollectionOf(Invoice)
    val tiersCollection = firestore.typedCollectionOf(Tier)
    val subscribersCollection = firestore.typedCollectionOf(Subscriber)

    val stripeKeysRepository = TypedCollectionReference<StripeKeys>(firestore.firestore["constants"])
    val stripeKeysEu = stripeKeysRepository["${production.envPrefix}-stripe-eu"].get()
    val stripeKeysUs = stripeKeysRepository["${production.envPrefix}-stripe-us"].get()
    val stripeClients = StripeClients(
        keysEu = SystemEnv.stripeKeyEu,
        keysUs = SystemEnv.stripeKeyUs,
    )
    val stripeService = StripeService(
        clients = stripeClients,
        pubSub = pubSub,
    )
    val stripePaymentMethods = StripePaymentMethodsService(
        clients = stripeClients,
        service = stripeService,
        pubSub = pubSub,
    )
    val webhookSecrets = mapOf(
        Currency.EUR to stripeKeysEu.webhookSecrets,
        Currency.USD to if (production) stripeKeysEu.webhookSecrets else stripeKeysUs.webhookSecrets,
    )
    val countryToVatMapping = VatMappingProvider(systemEnv("FLEXIBEE_PASSWORD")).countryToVatMapping()
    val lazyContext = lazyContext(environment)

    val stripeCouponService = StripeCouponService(stripeClients)
    val couponCommandService = CouponCommandService(
        stripeCouponService = stripeCouponService,
        lazyContext = lazyContext,
    )
    http4kInJetty(
        "Herohero Stripe service",
        SystemEnv.isProduction,
        isLocalHost,
        listOf(
            AccountWebhookController(
                webhookSecrets = webhookSecrets,
                accountCommandService = AccountCommandService(
                    stripeService = stripeService,
                    usersCollection = usersCollection,
                    pubSub = pubSub,
                    hostname = hostname,
                    lazyContext = lazyContext,
                ),
            ),
            CouponWebhookController(
                webhookSecrets = webhookSecrets,
                couponCommandService = couponCommandService,
            ),
            ChargeWebhookController(
                webhookSecrets = webhookSecrets,
                chargeCommandService = ChargeCommandService(lazyContext, stripeClients, usersCollection),
                pubSub = pubSub,
            ),
            CustomerWebhookController(
                webhookSecrets = webhookSecrets,
                customerCommandService = CustomerCommandService(
                    stripeService = stripeService,
                    production = production,
                    lazyContext = lazyContext,
                ),
            ),
            InvoiceWebhookController(
                webhookSecrets = webhookSecrets,
                invoiceCommandService = InvoiceCommandService(lazyContext, stripeService),
            ),
            PaymentMethodWebhookController(
                webhookSecrets = webhookSecrets,
                PaymentMethodCommandService(
                    usersCollection = usersCollection,
                    pubSub = pubSub,
                    stripeService = stripeService,
                    stripePaymentMethods = stripePaymentMethods,
                ),
            ),
            PayoutWebhookController(
                webhookSecrets = webhookSecrets,
                payoutCommandService = PayoutCommandService(
                    hostnameServices = hostnameServices,
                    pubSub = pubSub,
                    tiersCollection = tiersCollection,
                    invoicesCollection = invoicesCollection,
                    stripe = stripeService,
                    usersCollection = usersCollection,
                    countryToVatMapping = countryToVatMapping,
                ),
            ),
            ReportWebhookController(
                webhookSecrets = webhookSecrets,
                stripeClients = stripeClients,
                pubSub = pubSub,
            ),
            SubscriptionWebhookController(
                webhookSecrets = webhookSecrets,
                subscriptionCommandService = SubscriptionCommandService(
                    stripeService = stripeService,
                    usersCollection = usersCollection,
                    couponCommandService = couponCommandService,
                    pubSub = pubSub,
                    subscriptionService = StripeSubscriptionService(
                        clients = stripeClients,
                        paymentMethodsService = stripePaymentMethods,
                        production = production,
                        countryToVatMapping = countryToVatMapping,
                        stripeCouponService = stripeCouponService,
                    ),
                    stripeClients = stripeClients,
                    stripeSubscriberSaver = StripeSubscriberSaver(
                        subscribersCollection = subscribersCollection,
                        tiersCollection = tiersCollection,
                        appleSubscriptionService = AppleSubscriptionService(
                            AppleSigningService(production),
                            production,
                        ),
                        pubSub = pubSub,
                    ),
                    lazyContext = lazyContext,
                ),
            ),
        ),
    )
}

private fun lazyContext(environment: String): Lazy<DSLContext> {
    // if environment is not local, we have to trigger the lazy loading, so we know if a configuration is wrong
    // on the start
    try {
        ConnectorConnectionPool.dataSource
    } catch (e: Exception) {
        if (environment != "local") {
            throw e
        } else {
            log.warn("Failed to load data source, ignoring as run in local env: ${e.message}")
        }
    }

    return lazy {
        JooqSQL.context(ConnectorConnectionPool.dataSource)
    }
}
