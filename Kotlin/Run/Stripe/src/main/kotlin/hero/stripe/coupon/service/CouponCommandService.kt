package hero.stripe.coupon.service

import com.stripe.model.Coupon
import hero.baseutils.log
import hero.model.CouponMethod
import hero.model.CouponTarget
import hero.model.Currency
import hero.sql.jooq.Tables.COUPON
import hero.stripe.service.StripeCouponService
import org.jooq.DSLContext
import java.time.Instant

class CouponCommandService(
    private val stripeCouponService: StripeCouponService,
    lazyContext: Lazy<DSLContext>,
) {
    private val context: DSLContext by lazyContext

    fun execute(command: ProcessCoupon) {
        val coupon = stripeCouponService.getCouponOrNull(command.couponId, command.currency)
        if (coupon == null) {
            log.info("Marking coupon ${command.couponId} as deleted, if it's not marked yet")
            val rowsUpdated = context
                .update(COUPON)
                .set(COUPON.DELETED_AT, Instant.now())
                .set(COUPON.VALID, false)
                .where(COUPON.STRIPE_ID.eq(command.couponId).and(COUPON.DELETED_AT.isNull))
                .execute()

            log.info("Marked coupon ${command.couponId} as deleted, updated $rowsUpdated rows")
            return
        }
        log.info("Saving coupon ${coupon.id}")

        context
            .insertInto(COUPON)
            .set(COUPON.STRIPE_ID, coupon.id)
            .set(COUPON.CREATED_AT, coupon.createdAt)
            .set(COUPON.CURRENCY, coupon.currency)
            .set(COUPON.DURATION, coupon.duration)
            .set(COUPON.DURATION_IN_MONTHS, coupon.durationInMonths?.toInt())
            .set(COUPON.NAME, coupon.name)
            .set(COUPON.PERCENT_OFF, coupon.percentOff?.toDouble())
            .set(COUPON.MAX_REDEMPTIONS, coupon.maxRedemptions?.toInt())
            .set(COUPON.TIMES_REDEEMED, coupon.timesRedeemed.toInt())
            .set(COUPON.REDEEM_BY, coupon.redeemBy?.let { Instant.ofEpochSecond(it) })
            .set(COUPON.VALID, coupon.valid)
            // our metadata
            .set(COUPON.CREATOR_ID, coupon.creatorId)
            .set(COUPON.TARGET, coupon.target)
            .set(COUPON.METHOD, coupon.method)
            .set(COUPON.CAMPAIGN, coupon.campaign)
            .set(COUPON.TIER_ID, coupon.tierId)
            .set(COUPON.PURCHASED_BY, coupon.purchasedBy)
            .set(COUPON.AMOUNT_OFF, coupon.amountOff?.toInt())
            .onConflict(COUPON.STRIPE_ID)
            .doUpdate()
            // always update our metadata
            .set(COUPON.CREATOR_ID, coupon.creatorId)
            .set(COUPON.TARGET, coupon.target)
            .set(COUPON.METHOD, coupon.method)
            .set(COUPON.CAMPAIGN, coupon.campaign)
            .set(COUPON.TIER_ID, coupon.tierId)
            .set(COUPON.PURCHASED_BY, coupon.purchasedBy)
            // these fields are the only ones that can change on the coupon, the rest is immutable
            .set(COUPON.TIMES_REDEEMED, coupon.timesRedeemed.toInt())
            .set(COUPON.NAME, coupon.name)
            .set(COUPON.VALID, coupon.valid)
            // someone might have recreated the coupon, so we are marking it as non-deleted
            // maybe we should create a new id column and use the stripe id as unique only for non deleted
            // this way we could keep history of coupons
            .setNull(COUPON.DELETED_AT)
            .execute()

        log.info("Done saving coupon ${coupon.id}")
    }
}

data class ProcessCoupon(val couponId: String, val currency: Currency)

private val Coupon.createdAt
    get() = Instant.ofEpochSecond(created)

private val Coupon.creatorId
    get() = metadata["creatorId"] ?: error("Missing creator id for coupon $id")

private val Coupon.purchasedBy
    get() = metadata["purchasedByUserId"] ?: creatorId

private val Coupon.target
    get() = metadata["couponTarget"] ?: CouponTarget.CREATOR.name

private val Coupon.method
    get() = metadata["couponMethod"] ?: CouponMethod.VOUCHER.name

private val Coupon.campaign
    get() = metadata["campaign"]

private val Coupon.tierId
    get() = metadata["tierId"]
