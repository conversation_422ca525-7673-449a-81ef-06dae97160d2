package hero.api.subscriber.repository

import hero.baseutils.minusDays
import hero.baseutils.truncated
import hero.exceptions.http.BadRequestException
import hero.model.CouponMethod
import hero.model.Creator
import hero.model.Subscriber
import hero.model.SubscriberStatus.ACTIVE
import hero.model.SubscriberStatus.CANCELLED
import hero.model.SubscriberType
import hero.model.User
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.time.Instant
import kotlin.test.assertEquals

class SubscriberStripeRepositoryTest {
    private val baseExceptionMessage = "User 1 cannot resubscribe 2"

    @Test
    fun `throw if the user was not subscribed before`() {
        val subscriberRepository = mockk<SubscribersRepository>()
        every { subscriberRepository.getSubscriber(any(), any()) } returns null
        val underTest = SubscriberStripeRepository(
            stripe = mockk(),
            stripeSubscriptionService = mockk(),
            subscriberRepository = subscriberRepository,
            stripePricesCollection = mockk(),
            tierRepository = mockk(),
            userRepository = mockk(),
            firestore = mockk(),
            stripeSubscriberSaver = mockk(),
            lazyContext = mockk(),
            stripeCouponService = mockk(),
        )

        val exceptionReason = "because they were not subscribed before"
        val exception = assertThrows<BadRequestException> {
            underTest.resubscribe(
                user = User(id = "1", path = "1", name = "1", creator = Creator(tierId = "EUR05")),
                creator = User(id = "2", path = "2", name = "2", creator = Creator(tierId = "EUR05")),
                paymentMethodId = "paymentMethod",
                tierId = "EUR10",
                cardCreateType = null,
            )
        }
        assertEquals("$baseExceptionMessage $exceptionReason", exception.message)
    }

    @Test
    fun `throw if last subscription is still active`() {
        val subscriberRepository = mockk<SubscribersRepository>()
        every { subscriberRepository.getSubscriber(any(), any()) } returns Subscriber(
            status = ACTIVE,
            userId = "user",
            creatorId = "creator",
            subscribed = Instant.now().truncated(),
            subscriberType = SubscriberType.STRIPE,
            couponMethod = CouponMethod.VOUCHER,
            tierId = "EUR05",
        )

        val underTest = SubscriberStripeRepository(
            stripe = mockk(),
            stripeSubscriptionService = mockk(),
            subscriberRepository = subscriberRepository,
            stripePricesCollection = mockk(),
            tierRepository = mockk(),
            userRepository = mockk(),
            firestore = mockk(),
            stripeSubscriberSaver = mockk(),
            stripeCouponService = mockk(),
            lazyContext = mockk(),
        )

        val exceptionReason = "because the subscription is still active"

        val exception = assertThrows<BadRequestException> {
            underTest.resubscribe(
                user = User(id = "1", path = "1", name = "1", creator = Creator(tierId = "EUR05")),
                creator = User(id = "2", path = "2", name = "2", creator = Creator(tierId = "EUR05")),
                paymentMethodId = "paymentMethod",
                tierId = "EUR10",
                cardCreateType = null,
            )
        }
        assertEquals("$baseExceptionMessage $exceptionReason", exception.message)
    }

    @Test
    fun `throw if the passed tier does not match the tier from last subscription`() {
        val subscriberRepository = mockk<SubscribersRepository>()
        every { subscriberRepository.getSubscriber(any(), any()) } returns Subscriber(
            status = CANCELLED,
            tierId = "EUR5",
            userId = "user",
            creatorId = "creator",
            subscribed = Instant.now().truncated(),
            subscriberType = SubscriberType.STRIPE,
            couponMethod = CouponMethod.VOUCHER,
        )
        val underTest = SubscriberStripeRepository(
            stripe = mockk(),
            stripeSubscriptionService = mockk(),
            subscriberRepository = subscriberRepository,
            stripePricesCollection = mockk(),
            tierRepository = mockk(),
            userRepository = mockk(),
            firestore = mockk(),
            stripeSubscriberSaver = mockk(),
            stripeCouponService = mockk(),
            lazyContext = mockk(),
        )

        val exceptionReason = "with tier EUR10 because the original price was EUR5"
        val exception = assertThrows<BadRequestException> {
            underTest.resubscribe(
                user = User(id = "1", path = "1", name = "1", creator = Creator(tierId = "EUR05")),
                creator = User(id = "2", path = "2", name = "2", creator = Creator(tierId = "EUR05")),
                paymentMethodId = "paymentMethod",
                tierId = "EUR10",
                cardCreateType = null,
            )
        }
        assertEquals("$baseExceptionMessage $exceptionReason", exception.message)
    }

    @Test
    fun `throw if the last subscription expired more than 31 days ago`() {
        val subscriberRepository = mockk<SubscribersRepository>()
        every { subscriberRepository.getSubscriber(any(), any()) } returns Subscriber(
            status = CANCELLED,
            tierId = "EUR10",
            expires = Instant.now().minusDays(32),
            userId = "user",
            creatorId = "creator",
            subscribed = Instant.now().truncated(),
            subscriberType = SubscriberType.STRIPE,
            couponMethod = CouponMethod.VOUCHER,
        )
        val underTest = SubscriberStripeRepository(
            stripe = mockk(),
            stripeSubscriptionService = mockk(),
            subscriberRepository = subscriberRepository,
            stripePricesCollection = mockk(),
            tierRepository = mockk(),
            userRepository = mockk(),
            firestore = mockk(),
            stripeSubscriberSaver = mockk(),
            stripeCouponService = mockk(),
            lazyContext = mockk(),
        )

        val exceptionReason = "because subscription expired more than 31 days ago"
        val exception = assertThrows<BadRequestException> {
            underTest.resubscribe(
                user = User(id = "1", path = "1", name = "1", creator = Creator(tierId = "EUR05")),
                creator = User(id = "2", path = "2", name = "2", creator = Creator(tierId = "EUR05")),
                paymentMethodId = "paymentMethod",
                tierId = "EUR10",
                cardCreateType = null,
            )
        }
        assertEquals("$baseExceptionMessage $exceptionReason", exception.message)
    }
}
