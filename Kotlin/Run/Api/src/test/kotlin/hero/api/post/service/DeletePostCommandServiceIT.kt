package hero.api.post.service

import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ForbiddenException
import hero.model.topics.PostState
import hero.model.topics.PostState.PUBLISHED
import hero.model.topics.PostStateChange
import hero.model.topics.PostStateChanged
import hero.sql.jooq.Tables.POST
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper.TestCollections
import hero.test.TestRepositories
import hero.test.logging.TestLogger
import hero.test.time.TestClock
import io.mockk.slot
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.Clock
import java.time.Instant

class DeletePostCommandServiceIT : IntegrationTest() {
    private val expectedTimestamp = Instant.ofEpochSecond(1755450161)

    @Nested
    inner class DeleteCreatorPost {
        @Test
        fun `should delete a post by the post owner`() {
            val underTest = prepareService()

            testHelper.createUser("pablo")
            val post = testHelper.createPost("pablo", state = PUBLISHED)

            underTest.execute(DeleteCreatorPost("pablo", post.id))

            // Verify post state is updated in Firestore
            val updatedPostFromFirestore = TestCollections.postsCollection[post.id].get()
            assertThat(updatedPostFromFirestore.state).isEqualTo(PostState.DELETED)
            assertThat(updatedPostFromFirestore.deletedAt).isEqualTo(expectedTimestamp)

            // Verify post state is updated in PostgreSQL
            val postRecord = testContext.selectFrom(POST)
                .where(POST.ID.eq(post.id))
                .fetchSingle()

            assertThat(postRecord.state).isEqualTo(PostState.DELETED.name)
            assertThat(postRecord.deletedAt).isEqualTo(expectedTimestamp)

            // Verify PubSub event is published
            val publishedEvent = slot<PostStateChanged>()
            verify { pubSubMock.publish(capture(publishedEvent)) }
            assertThat(publishedEvent.captured.stateChange).isEqualTo(PostStateChange.DELETED)
            assertThat(publishedEvent.captured.post.id).isEqualTo(post.id)
        }

        @Test
        fun `should delete a post in a community by the community owner`() {
            val underTest = prepareService()

            testHelper.createUser("owner")
            testHelper.createUser("author")
            val community = testHelper.createCommunity("owner")
            val post = testHelper.createPost(
                userId = "author",
                state = PUBLISHED,
                communityId = community.id,
            )

            underTest.execute(DeleteCreatorPost("owner", post.id))

            // Verify post state is updated
            val updatedPost = TestCollections.postsCollection[post.id].get()
            assertThat(updatedPost.state).isEqualTo(PostState.DELETED)
            assertThat(updatedPost.deletedAt).isNotNull()

            // Verify PubSub event is published
            val publishedEvent = slot<PostStateChanged>()
            verify { pubSubMock.publish(capture(publishedEvent)) }
            assertThat(publishedEvent.captured.stateChange).isEqualTo(PostStateChange.DELETED)
        }

        @Test
        fun `should skip deletion if post is already deleted`() {
            val underTest = prepareService()

            testHelper.createUser("pablo")
            val post = testHelper.createPost("pablo", state = PostState.DELETED)

            underTest.execute(DeleteCreatorPost("pablo", post.id))

            // Verify no PubSub event is published since post was already deleted
            verify(exactly = 0) { pubSubMock.publish(any<PostStateChanged>()) }
        }

        @Test
        fun `user cannot delete another user's post`() {
            val underTest = prepareService()

            testHelper.createUser("pablo")
            testHelper.createUser("filip")
            val post = testHelper.createPost("pablo", state = PUBLISHED)

            assertThatExceptionOfType(ForbiddenException::class.java)
                .isThrownBy {
                    underTest.execute(DeleteCreatorPost("filip", post.id))
                }
                .withMessage("User filip cannot delete post ${post.id}")
        }

        @Test
        fun `creator can delete comments on their posts`() {
            val underTest = prepareService()

            testHelper.createUser("creator")
            testHelper.createUser("commenter")
            val post = testHelper.createPost("creator", state = PUBLISHED)
            val comment = testHelper.createPost("commenter", parentId = post.id, state = PUBLISHED)
            underTest.execute(DeleteCreatorPost("creator", comment.id))

            // Verify post state is updated
            val updatedPost = TestCollections.postsCollection[comment.id].get()
            assertThat(updatedPost.state).isEqualTo(PostState.DELETED)

            // Verify PubSub event is published
            val publishedEvent = slot<PostStateChanged>()
            verify { pubSubMock.publish(capture(publishedEvent)) }
            assertThat(publishedEvent.captured.stateChange).isEqualTo(PostStateChange.DELETED)
        }

        @Test
        fun `user cannot delete post in community they don't own`() {
            val underTest = prepareService()

            testHelper.createUser("owner")
            testHelper.createUser("author")
            testHelper.createUser("other")
            val community = testHelper.createCommunity("owner")
            val post = testHelper.createPost(
                userId = "author",
                state = PUBLISHED,
                communityId = community.id,
            )

            assertThatExceptionOfType(ForbiddenException::class.java)
                .isThrownBy {
                    underTest.execute(DeleteCreatorPost("other", post.id))
                }
                .withMessage("User other cannot delete post ${post.id} in community ${community.id}")
        }

        @Test
        fun `post author can delete their own post even in a community`() {
            val underTest = prepareService()

            testHelper.createUser("owner")
            testHelper.createUser("author")
            val community = testHelper.createCommunity("owner")
            val post = testHelper.createPost(
                userId = "author",
                state = PUBLISHED,
                communityId = community.id,
            )

            underTest.execute(DeleteCreatorPost("author", post.id))

            // Verify post state is updated
            val updatedPost = TestCollections.postsCollection[post.id].get()
            assertThat(updatedPost.state).isEqualTo(PostState.DELETED)

            // Verify PubSub event is published
            val publishedEvent = slot<PostStateChanged>()
            verify { pubSubMock.publish(capture(publishedEvent)) }
            assertThat(publishedEvent.captured.stateChange).isEqualTo(PostStateChange.DELETED)
        }
    }

    @Nested
    inner class DeleteComment {
        @Test
        fun `should delete a comment by the comment author`() {
            val underTest = prepareService()

            val post = testHelper.createPost("author", state = PUBLISHED)
            val comment = testHelper.createPost("commenter", parentId = post.id, state = PUBLISHED)

            underTest.execute(DeleteComment("commenter", comment.id))

            // verify comment state is updated in Firestore
            with(TestCollections.postsCollection[comment.id].get()) {
                assertThat(state).isEqualTo(PostState.DELETED)
                assertThat(deletedAt).isEqualTo(expectedTimestamp)
            }

            // verify comment state is updated in PostgreSQL
            with(testContext.selectFrom(POST).where(POST.ID.eq(comment.id)).fetchSingle()) {
                assertThat(state).isEqualTo(PostState.DELETED.name)
                assertThat(deletedAt).isEqualTo(expectedTimestamp)
            }

            // verify PubSub event is published
            val publishedEvent = slot<PostStateChanged>()
            verify { pubSubMock.publish(capture(publishedEvent)) }
            assertThat(publishedEvent.captured.stateChange).isEqualTo(PostStateChange.DELETED)
            assertThat(publishedEvent.captured.post.id).isEqualTo(comment.id)
        }

        @Test
        fun `comment author can delete their own comment even in a community`() {
            val underTest = prepareService()

            testHelper.createUser("owner")
            val community = testHelper.createCommunity("owner")
            val post = testHelper.createPost(userId = "author", state = PUBLISHED, communityId = community.id)
            val comment = testHelper.createPost(userId = "commenter", parentId = post.id, state = PUBLISHED)

            underTest.execute(DeleteComment("commenter", comment.id))

            // Verify comment state is updated
            val updatedComment = TestCollections.postsCollection[comment.id].get()
            assertThat(updatedComment.state).isEqualTo(PostState.DELETED)

            // Verify PubSub event is published
            val publishedEvent = slot<PostStateChanged>()
            verify { pubSubMock.publish(capture(publishedEvent)) }
            assertThat(publishedEvent.captured.stateChange).isEqualTo(PostStateChange.DELETED)
        }

        @Disabled("Not implemented yet")
        @Test
        fun `post author should always be able to delete comments on his posts`() {
            val underTest = prepareService()

            testHelper.createUser("author")
            testHelper.createUser("commenter")
            val post = testHelper.createPost("author", state = PUBLISHED)
            val comment = testHelper.createPost("commenter", parentId = post.id, state = PUBLISHED)

            underTest.execute(DeleteComment("author", comment.id))

            // Verify comment state is updated
            val updatedComment = TestCollections.postsCollection[comment.id].get()
            assertThat(updatedComment.state).isEqualTo(PostState.DELETED)
            assertThat(updatedComment.deletedAt).isNotNull()

            // Verify PubSub event is published
            val publishedEvent = slot<PostStateChanged>()
            verify { pubSubMock.publish(capture(publishedEvent)) }
            assertThat(publishedEvent.captured.stateChange).isEqualTo(PostStateChange.DELETED)
        }

        @Test
        fun `owner of the community is able to delete all comments in his community`() {
            val underTest = prepareService()

            testHelper.createUser("owner")
            val community = testHelper.createCommunity("owner")
            val post = testHelper.createPost(userId = "author", state = PUBLISHED, communityId = community.id)
            val comment = testHelper.createPost(userId = "commenter", parentId = post.id, state = PUBLISHED)

            underTest.execute(DeleteComment("owner", comment.id))

            with(TestCollections.postsCollection[comment.id].get()) {
                assertThat(state).isEqualTo(PostState.DELETED)
                assertThat(deletedAt).isNotNull()
            }

            val publishedEvent = slot<PostStateChanged>()
            verify { pubSubMock.publish(capture(publishedEvent)) }
            assertThat(publishedEvent.captured.stateChange).isEqualTo(PostStateChange.DELETED)
        }

        @Test
        fun `should skip deletion if comment is already deleted`() {
            val underTest = prepareService()

            val post = testHelper.createPost("author", state = PUBLISHED)
            val comment = testHelper.createPost("commenter", parentId = post.id, state = PostState.DELETED)

            underTest.execute(DeleteComment("commenter", comment.id))

            // Verify no PubSub event is published since comment was already deleted
            verify(exactly = 0) { pubSubMock.publish(any<PostStateChanged>()) }
        }

        @Test
        fun `should throw BadRequestException when trying to delete a post that is not a comment`() {
            val underTest = prepareService()

            val post = testHelper.createPost("author", state = PUBLISHED)

            assertThatExceptionOfType(BadRequestException::class.java)
                .isThrownBy {
                    underTest.execute(DeleteComment("author", post.id))
                }
                .withMessage("Post ${post.id} is not a comment")
        }

        @Test
        fun `user cannot delete another user's comment`() {
            val underTest = prepareService()

            val post = testHelper.createPost("author", state = PUBLISHED)
            val comment = testHelper.createPost("commenter", parentId = post.id, state = PUBLISHED)

            assertThatExceptionOfType(ForbiddenException::class.java)
                .isThrownBy {
                    underTest.execute(DeleteComment("other", comment.id))
                }
                .withMessage("User other cannot delete post ${comment.id}")
        }

        @Test
        fun `user cannot delete other's user comment in community they don't own`() {
            val underTest = prepareService()

            testHelper.createUser("owner")
            val community = testHelper.createCommunity("owner")
            val post = testHelper.createPost(userId = "author", state = PUBLISHED, communityId = community.id)
            val comment = testHelper.createPost(userId = "commenter", parentId = post.id, state = PUBLISHED)

            assertThatExceptionOfType(ForbiddenException::class.java)
                .isThrownBy {
                    underTest.execute(DeleteComment("other", comment.id))
                }
                .withMessage("User other cannot delete post ${comment.id} in community ${community.id}")
        }
    }

    private fun prepareService(testClock: Clock = TestClock(expectedTimestamp)): DeletePostCommandService =
        DeletePostCommandService(
            lazyContext = lazyTestContext,
            postsCollection = TestCollections.postsCollection,
            postRepository = TestRepositories.postRepository,
            pubSub = pubSubMock,
            logger = TestLogger,
            clock = testClock,
        )
}
