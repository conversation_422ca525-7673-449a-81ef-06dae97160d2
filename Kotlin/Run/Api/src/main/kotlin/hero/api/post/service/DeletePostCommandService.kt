package hero.api.post.service

import hero.core.logging.Logger
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ForbiddenException
import hero.gcloud.PubSub
import hero.gcloud.TypedCollectionReference
import hero.model.Post
import hero.model.topics.PostState
import hero.model.topics.PostStateChange
import hero.model.topics.PostStateChanged
import hero.repository.post.PostRepository
import hero.sql.jooq.Tables
import org.jooq.DSLContext
import java.time.Clock
import java.time.Instant
import java.util.UUID

class DeletePostCommandService(
    lazyContext: Lazy<DSLContext>,
    private val postsCollection: TypedCollectionReference<Post>,
    private val postRepository: PostRepository,
    private val pubSub: PubSub,
    private val logger: Logger,
    private val clock: Clock = Clock.systemUTC(),
) {
    private val context by lazyContext

    fun execute(command: DeleteComment) {
        val comment = postRepository.getById(command.commentId)

        if (comment.parentId == null) {
            throw BadRequestException("Post ${comment.id} is not a comment")
        }

        deletePost(comment, command.userId)
    }

    fun execute(command: DeleteCreatorPost) {
        val post = postRepository.getById(command.postId)

        deletePost(post, command.userId)
    }

    private fun deletePost(
        post: Post,
        userId: String,
    ) {
        if (post.state == PostState.DELETED) {
            logger.info("Post ${post.id} is already deleted, skipping.", mapOf("userId" to userId))
            return
        }

        val rootPost = postRepository.getRootPost(post)
        val communityId = rootPost.communityId
        if (rootPost.userId != userId && post.userId != userId && communityId == null) {
            throw ForbiddenException(
                "User $userId cannot delete post ${post.id}",
                mapOf("userId" to userId, "postId" to post.id),
            )
        }

        if (post.userId != userId && communityId != null) {
            val ownerId = context.select(Tables.COMMUNITY.OWNER_ID)
                .from(Tables.COMMUNITY)
                .where(Tables.COMMUNITY.ID.eq(UUID.fromString(communityId)))
                .fetchSingle()
                .value1()
            if (ownerId != userId) {
                throw ForbiddenException(
                    "User $userId cannot delete post ${post.id} in community $communityId",
                    mapOf("userId" to userId, "postId" to post.id),
                )
            }
        }

        val now = Instant.now(clock)
        postsCollection[post.id].field(Post::state).update(PostState.DELETED)
        postsCollection[post.id].field(Post::deletedAt).update(now)
        context
            .update(Tables.POST)
            .set(Tables.POST.STATE, PostState.DELETED.name)
            .set(Tables.POST.DELETED_AT, now)
            .where(Tables.POST.ID.eq(post.id)).execute()

        pubSub.publish(PostStateChanged(PostStateChange.DELETED, post))
    }
}

data class DeleteCreatorPost(val userId: String, val postId: String)

data class DeleteComment(val userId: String, val commentId: String)
