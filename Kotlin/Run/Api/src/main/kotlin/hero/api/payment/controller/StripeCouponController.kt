package hero.api.payment.controller

import hero.api.payment.controller.dto.CouponDto
import hero.api.payment.controller.dto.CouponDtoAttributes
import hero.api.payment.controller.dto.CouponDtoRelationships
import hero.api.payment.controller.dto.CouponInviteRequest
import hero.api.payment.controller.dto.CouponPurchaseRequest
import hero.api.payment.controller.dto.CouponResponseDto
import hero.api.payment.controller.dto.CouponResponseDtoIncluded
import hero.api.payment.controller.dto.CouponsResponseDto
import hero.api.payment.controller.dto.exampleGetCouponsResponse
import hero.api.payment.controller.dto.examplePaymentCancelled
import hero.api.payment.controller.dto.examplePaymentRequiresAction
import hero.api.payment.controller.dto.examplePaymentSucceeeded
import hero.api.payment.service.StripePaymentsService
import hero.api.user.repository.TiersRepository
import hero.api.user.repository.UsersRepository
import hero.api.user.repository.toDto
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ConflictException
import hero.exceptions.http.NotFoundException
import hero.http4k.controller.QueryUtils.userId
import hero.http4k.extensions.authorization
import hero.http4k.extensions.body
import hero.http4k.extensions.example
import hero.http4k.extensions.get
import hero.http4k.extensions.lens
import hero.http4k.extensions.post
import hero.model.CouponProvider
import hero.model.CouponTarget
import hero.model.FREE_SUBSCRIBER_TIER_ID
import hero.model.Role
import hero.model.Tier
import hero.model.TierDtoRelationship
import hero.model.UserDtoRelationship
import hero.stripe.service.StripeCouponService
import hero.stripe.service.StripeService
import hero.stripe.service.couponMethod
import org.http4k.contract.ContractRoute
import org.http4k.contract.div
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Header
import org.http4k.lens.Path
import org.http4k.lens.regex
import java.time.Instant

class StripeCouponController(
    private val stripePaymentsService: StripePaymentsService,
    private val stripeCouponService: StripeCouponService,
    private val stripeService: StripeService,
    private val tierRepository: TiersRepository,
    private val userRepository: UsersRepository,
) {
    @Suppress("unused")
    val routePostPurchaseCreatorsCoupon: ContractRoute =
        ("/v1/users" / Path.userId().of("creatorId") / "coupons").post(
            summary = "Initiates a purchase of creator's coupon to be used to pay for a subscription.",
            parameters = object {
                val authorization = Header.authorization()
            },
            receiving = CouponPurchaseRequest(
                paymentMethodId = "pm_EtGYv3abB0",
                months = 3,
            ),
            tag = "Coupons",
            responses = listOf(
                Status.OK example examplePaymentSucceeeded,
                Status.ACCEPTED example examplePaymentRequiresAction,
                Status.UNPROCESSABLE_ENTITY example examplePaymentCancelled,
            ),
            handler = { request, _, creatorId, _ ->
                val body = lens<CouponPurchaseRequest>()[request]
                val user = userRepository.get(request)
                val creator = userRepository.get(creatorId)

                if (!creator.creator.active) {
                    throw ConflictException(
                        "User ${creator.id} is not active creator.",
                        mapOf("userId" to user.id, "creatorId" to creator.id),
                    )
                }

                if (!creator.hasGiftsAllowed && user.role != Role.MODERATOR) {
                    throw ConflictException(
                        "Creator ${creator.id} is not ready (too small) to sell coupons.",
                        mapOf("userId" to user.id, "creatorId" to creator.id),
                    )
                }

                val (paymentIntent, coupon) = stripePaymentsService.purchaseCoupon(
                    paymentMethodId = body.paymentMethodId,
                    months = body.months,
                    creator = creator,
                    user = user,
                )

                val couponResponse = PaymentResponse(
                    PaymentResponseAttributes(
                        status = paymentIntent.status,
                        createdAt = paymentIntent.created,
                        paymentIntentClientSecret = paymentIntent.intent?.clientSecret,
                        couponCode = coupon?.metadata["couponCode"],
                        subscriptionStatus = null,
                    ),
                    PaymentResponseRelationships(
                        user = UserDtoRelationship(user.id),
                        creator = UserDtoRelationship(creatorId),
                        post = null,
                    ),
                )

                Response(paymentIntent.httpStatus)
                    .body(couponResponse)
            },
        )

    @Suppress("unused")
    val routePostCreateCreatorsCoupon: ContractRoute =
        ("/v1/coupons").post(
            summary = "Creates a creator's coupon to be used to discount/invite for a subscription.",
            parameters = object {
                val authorization = Header.authorization()
            },
            receiving = CouponInviteRequest(
                couponCode = "AIRBANK_2025",
                percentOff = 30,
                months = 3,
                days = 3,
                redemptions = 10,
                count = 10,
                campaign = "Airbank TV ad",
                redeemBy = Instant.now(),
            ),
            tag = "Coupons",
            responses = listOf(
                // TODO
                Status.OK example examplePaymentSucceeeded,
            ),
            handler = { request, _ ->
                val body = lens<CouponInviteRequest>()[request]
                val user = userRepository.get(request)

                if (!user.creator.active && user.creator.tierId != FREE_SUBSCRIBER_TIER_ID) {
                    throw ConflictException(
                        "User ${user.id} is not active creator.",
                        mapOf("userId" to user.id),
                    )
                }

                if (body.count > 100) {
                    throw BadRequestException("Cannot generate more than 100 coupons at once (to avoid timeouts).")
                }

                val currency = Tier.ofId(user.creator.tierId).currency
                if (body.couponCode != null) {
                    if (body.count > 1) {
                        throw BadRequestException(
                            "Cannot generate multiple coupons with custom couponCode.",
                            mapOf("userId" to user.id, "couponCode" to body.couponCode),
                        )
                    }
                    if (!body.couponCode.matches("[A-Z0-9]{3,20}".toRegex())) {
                        throw BadRequestException(
                            "Coupon code must be 3-20 characters long and contain only uppercase letters and numbers.",
                            mapOf("userId" to user.id, "couponCode" to body.couponCode),
                        )
                    }
                    val existingCoupon = stripeCouponService.getCouponOrNull(body.couponCode, user.id, currency)
                    if (existingCoupon != null) {
                        throw ConflictException(
                            "Coupon ${body.couponCode} for ${user.id} already exists.",
                            mapOf("userId" to user.id, "couponCode" to body.couponCode),
                        )
                    }
                }

                val coupons = (1..body.count).toList()
                    .parallelStream()
                    .map {
                        stripeCouponService.createCoupon(
                            couponCode = body.couponCode ?: stripeCouponService.generateCouponCode(user.id, currency),
                            purchasedByUserId = user.id,
                            creatorId = user.id,
                            tier = null,
                            price = null,
                            months = body.months,
                            days = body.days,
                            percentOff = body.percentOff,
                            redemptions = body.redemptions,
                            campaign = body.campaign,
                            currency = currency,
                            redeemBy = body.redeemBy,
                        )
                    }
                    .map { coupon ->
                        val days = coupon.metadata["days"]?.toInt()
                        CouponDto(
                            id = coupon.metadata["couponCode"] ?: coupon.id,
                            attributes = CouponDtoAttributes(
                                // in case of "day-trials" we don't actually use the coupon
                                // as we create a trial, so we have to "pretend" the coupon discount is 100 %
                                percentOff = if (days != null) 100 else coupon.percentOff?.toInt(),
                                months = coupon.durationInMonths?.toInt(),
                                days = days,
                                redemptions = coupon.maxRedemptions.toInt(),
                                redeemBy = coupon.redeemBy?.let { Instant.ofEpochSecond(it) },
                                target = CouponTarget.valueOf(coupon.metadata["couponType"].toString()),
                                provider = CouponProvider.STRIPE,
                                method = coupon.couponMethod(),
                                campaign = coupon.metadata["campaign"],
                            ),
                            relationships = CouponDtoRelationships(
                                tier = null,
                                creator = UserDtoRelationship(user.id),
                            ),
                        )
                    }

                Response(Status.OK)
                    .body(CouponsResponseDto(coupons.toList()))
            },
        )

    @Suppress("unused")
    val routeGetCoupon: ContractRoute =
        ("/v1/users" / Path.userId().of("creatorId") / "coupons" / Path.regex("([A-Z]+)").of("couponId")).get(
            summary = "Gets coupon details to be shown on PDF document.",
            parameters = object {},
            tag = "Coupons",
            responses = listOf(
                Status.OK example exampleGetCouponsResponse,
                Status.NOT_FOUND example Unit,
            ),
            handler = { request, _, creatorId, _, couponId ->
                val user = userRepository.get(creatorId)
                val currency = Tier.ofId(user.creator.tierId).currency
                val coupon = stripeCouponService.getCouponOrNull(creatorId, couponId, currency)
                    ?: throw NotFoundException("Coupon $couponId was not found.")
                // This endpoint is intentionally not authorized so that links can be sent to the gifted person.
                // I considered this safe for following reasons:
                // 1. This endpoint (and the coupon PDF generator) will be hidden behind Cloudflare's captcha
                //    from the public to avoid scanning.
                // 2. Coupons with length > 8 combined with creatorId will be really hard to guess.
                // 3. We will return NotFound instead of Forbidden to make scanning more difficult
                //    when creator does not match.
                if (creatorId != coupon.metadata["creatorId"]) {
                    // creatorId in url does not match the one in coupon metadata
                    throw NotFoundException("Coupon $couponId was not found.")
                }
                val tier = coupon.metadata["tierId"]?.let { tierRepository[it] }
                val creator = userRepository.get(coupon.metadata["creatorId"]!!)
                val days = coupon.metadata["days"]?.toInt()

                val response = CouponResponseDto(
                    data = CouponDto(
                        id = couponId,
                        attributes = CouponDtoAttributes(
                            // in case of "day-trials" we don't actually use the coupon
                            // as we create a trial, so we have to "pretend" the coupon discount is 100 %
                            percentOff = if (days != null) 100 else coupon.percentOff?.toInt(),
                            months = coupon.durationInMonths?.toInt(),
                            days = days,
                            redemptions = coupon.maxRedemptions.toInt(),
                            redeemBy = coupon.redeemBy?.let { Instant.ofEpochSecond(it) },
                            target = CouponTarget.valueOf(coupon.metadata["couponType"].toString()),
                            provider = CouponProvider.STRIPE,
                            method = coupon.couponMethod(),
                            // we intentionally pass null as this endpoint is widely available
                            campaign = null,
                        ),
                        relationships = CouponDtoRelationships(
                            tier = tier?.let { TierDtoRelationship(it.id) },
                            creator = UserDtoRelationship(creator.id),
                        ),
                    ),
                    included = CouponResponseDtoIncluded(
                        tiers = listOfNotNull(tier?.toDto()),
                        users = listOf(userRepository.toDto(creator, false, emptyList())),
                    ),
                )

                Response(Status.OK).body(response)
            },
        )
}
