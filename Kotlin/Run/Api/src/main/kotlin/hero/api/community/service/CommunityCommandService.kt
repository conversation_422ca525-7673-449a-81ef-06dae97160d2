package hero.api.community.service

import hero.exceptions.http.ConflictException
import hero.exceptions.http.ForbiddenException
import hero.gcloud.PubSub
import hero.model.Community
import hero.model.ImageAsset
import hero.model.topics.CommunityCreated
import hero.repository.community.CommunityRepository
import hero.repository.user.UserRepository
import hero.sql.jooq.Tables
import org.jooq.DSLContext
import java.time.Clock
import java.time.Instant
import java.util.UUID

class CommunityCommandService(
    private val communityRepository: CommunityRepository,
    private val userRepository: UserRepository,
    private val pubSub: PubSub,
    lazyContext: Lazy<DSLContext>,
    private val clock: Clock = Clock.systemUTC(),
) {
    val context: DSLContext by lazyContext

    fun execute(command: CreateCommunity): Community {
        val creator = userRepository.getById(command.userId)

        if (false && !creator.canCreateCommunity) {
            throw ForbiddenException("User ${command.userId} cannot create communities")
        }

        if (communityRepository.findByOwnerId(command.userId).isNotEmpty()) {
            throw ConflictException("User ${command.userId} already has a community")
        }

        val now = Instant.now(clock)
        val community = Community(
            id = UUID.randomUUID(),
            name = creator.name,
            description = "Community",
            slug = creator.path,
            ownerId = command.userId,
            membersCount = 1,
            image = creator.image,
            createdAt = now,
            updatedAt = now,
            deletedAt = null,
            threadsCount = 0,
        )

        communityRepository.save(community)
        pubSub.publish(CommunityCreated(community.id))
        context
            .update(Tables.USER)
            .set(Tables.USER.OWNED_COMMUNITIES_COUNT, Tables.USER.OWNED_COMMUNITIES_COUNT.plus(1))
            .where(Tables.USER.ID.eq(command.userId))
            .execute()

        return community
    }

    fun execute(command: UpdateCommunity): Community {
        val community = communityRepository.getById(command.communityId)
        if (command.userId != community.ownerId) {
            throw ForbiddenException("User ${command.userId} cannot update community ${command.communityId}")
        }

        if (command.slug != community.slug) {
            val communityWithSlug = context
                .selectFrom(Tables.COMMUNITY)
                .where(Tables.COMMUNITY.SLUG.eq(command.slug))
                .fetchOne()

            if (communityWithSlug != null) {
                throw ConflictException("Community with slug ${command.slug} already exists")
            }
        }

        val updatedCommunity = community.copy(
            name = command.name,
            description = command.description,
            image = command.image,
            slug = command.slug,
            updatedAt = Instant.now(clock),
        )
        communityRepository.save(updatedCommunity)

        return updatedCommunity
    }
}

data class CreateCommunity(val userId: String)

data class UpdateCommunity(
    val communityId: UUID,
    val userId: String,
    val name: String,
    val description: String,
    val image: ImageAsset?,
    val slug: String,
)
