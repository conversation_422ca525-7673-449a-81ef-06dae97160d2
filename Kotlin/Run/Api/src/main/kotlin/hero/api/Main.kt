package hero.api

import hero.api.category.controller.CategoriesController
import hero.api.category.repository.CategoriesRepository
import hero.api.common.LivenessController
import hero.api.community.controller.CommunitiesController
import hero.api.community.service.CommunityCommandService
import hero.api.community.service.CommunityQueryService
import hero.api.device.controller.DevicesController
import hero.api.device.service.DeviceCommandService
import hero.api.invoice.controller.InvoicesController
import hero.api.invoice.service.InvoiceService
import hero.api.library.controller.LibraryController
import hero.api.library.service.LibraryCommandService
import hero.api.library.service.LibraryQueryService
import hero.api.messages.controller.MessageThreadsController
import hero.api.messages.controller.MessageThreadsControllerDeprecated
import hero.api.messages.service.MessageCommandService
import hero.api.messages.service.MessageQueryService
import hero.api.messages.service.MessageThreadQueryService
import hero.api.messages.service.MessageThreadService
import hero.api.notification.controller.NotificationSettingsController
import hero.api.notification.controller.NotificationsController
import hero.api.notification.service.NotificationCommandService
import hero.api.notification.service.NotificationQueryService
import hero.api.notification.service.NotificationSettingsCommandService
import hero.api.notification.service.NotificationSettingsQueryService
import hero.api.payment.controller.StripeAppleController
import hero.api.payment.controller.StripeController
import hero.api.payment.controller.StripeCouponController
import hero.api.payment.controller.StripeWebhookController
import hero.api.payment.controller.TiersController
import hero.api.payment.service.AppleCallbackParser
import hero.api.payment.service.ApplePricingService
import hero.api.payment.service.StripeAppleMirroringService
import hero.api.payment.service.StripePaymentsService
import hero.api.post.controller.PollsController
import hero.api.post.controller.PostsController
import hero.api.post.controller.PostsJsonApiController
import hero.api.post.repository.PostRepositoryDeprecated
import hero.api.post.service.CommentCommandService
import hero.api.post.service.CommentQueryService
import hero.api.post.service.CreatorPostCommandService
import hero.api.post.service.CreatorPostQueryService
import hero.api.post.service.DeletePostCommandService
import hero.api.post.service.PollCommandService
import hero.api.post.service.PollQueryService
import hero.api.post.service.PostQueryService
import hero.api.post.service.PostService
import hero.api.post.service.PostServiceDeprecated
import hero.api.post.service.PostVoteCommandService
import hero.api.statistics.controller.StatisticsController
import hero.api.statistics.service.IncomeQueryService
import hero.api.statistics.service.PostStatisticsQueryService
import hero.api.statistics.service.SubscriberStatisticsQueryService
import hero.api.subscriber.controller.SubscribeRequestsController
import hero.api.subscriber.controller.SubscriberCallToActionController
import hero.api.subscriber.controller.SubscribersController
import hero.api.subscriber.repository.SubscriberStripeRepository
import hero.api.subscriber.repository.SubscribersRepository
import hero.api.subscriber.service.SubscribeRequestCommandService
import hero.api.subscriber.service.SubscribeRequestQueryService
import hero.api.subscriber.service.SubscriberQueryService
import hero.api.subscriber.service.SubscriberReportService
import hero.api.user.controller.AdminUsersController
import hero.api.user.controller.CountriesController
import hero.api.user.controller.FeaturedCreatorsController
import hero.api.user.controller.StoresController
import hero.api.user.controller.UserFactoryController
import hero.api.user.controller.UsersController
import hero.api.user.controller.UsersJsonApiController
import hero.api.user.repository.ImageRepository
import hero.api.user.repository.RssFeedsRepository
import hero.api.user.repository.TiersRepository
import hero.api.user.repository.UserIdGenerator
import hero.api.user.repository.UsersRepository
import hero.api.user.service.DeleteUserCommandService
import hero.api.user.service.FeaturedCreatorsQueryService
import hero.api.user.service.SearchUsersQueryService
import hero.api.user.service.UserCommandService
import hero.api.user.service.UserMediaCommandService
import hero.api.user.service.UserQueryService
import hero.api.user.service.UserRelationsService
import hero.api.watch.service.WatchActivityCommandService
import hero.baseutils.SystemEnv
import hero.baseutils.envPrefix
import hero.baseutils.log
import hero.baseutils.systemEnv
import hero.gcloud.FirestoreFulltextService
import hero.gcloud.FulltextIndex
import hero.gcloud.PubSub
import hero.gcloud.TypedCollectionReference
import hero.gcloud.firestore
import hero.gcloud.get
import hero.gcloud.typedCollectionOf
import hero.gjirafa.GjirafaLivestreamsService
import hero.gjirafa.GjirafaStatsService
import hero.gjirafa.GjirafaUploadsService
import hero.http4k.http4kInJetty
import hero.messaging.initFirebaseMessaging
import hero.model.AppleCharge
import hero.model.Category
import hero.model.Currency
import hero.model.Invoice
import hero.model.MessageThread
import hero.model.Path
import hero.model.Post
import hero.model.PostPayment
import hero.model.RssFeed
import hero.model.SavedPost
import hero.model.Subscriber
import hero.model.Tier
import hero.model.User
import hero.model.UserStore
import hero.repository.community.CommunityRepository
import hero.repository.notification.NotificationRepository
import hero.repository.notification.NotificationSettingsRepository
import hero.repository.post.PostRepository
import hero.repository.session.SessionRepository
import hero.repository.subscription.SubscribeRequestRepository
import hero.repository.user.UserRepository
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import hero.stripe.model.StripeKeys
import hero.stripe.model.StripePrice
import hero.stripe.service.AppleSigningService
import hero.stripe.service.AppleSubscriptionService
import hero.stripe.service.CancelSubscriptionCommandService
import hero.stripe.service.StripeAccountService
import hero.stripe.service.StripeClients
import hero.stripe.service.StripeCouponService
import hero.stripe.service.StripePaymentMethodsService
import hero.stripe.service.StripeService
import hero.stripe.service.StripeSubscriberSaver
import hero.stripe.service.StripeSubscriptionService
import hero.stripe.service.VatMappingProvider
import org.jooq.DSLContext

fun main() {
    val environment = SystemEnv.environment
    log.info("Service is starting in env: $environment")
    val hostname = SystemEnv.hostname
    val hostnameServices = SystemEnv.hostnameServices
    val projectId = SystemEnv.cloudProject
    val isLocalHost = systemEnv("LOG_APPENDER") != "ConsoleFluentD"
    val production = SystemEnv.isProduction
    val pubSub = PubSub(environment, projectId)
    val firestoreRef = firestore(projectId, production)
    val lazyContext = lazyContext(environment)

    val typedPostsCollection = firestoreRef.typedCollectionOf(Post)
    val savedPostsCollection = firestoreRef.typedCollectionOf(SavedPost)
    val postPaymentsCollection = firestoreRef.typedCollectionOf(PostPayment)
    val messageThreadsCollection = firestoreRef.typedCollectionOf(MessageThread)
    val subscribersCollection = firestoreRef.typedCollectionOf(Subscriber)
    val firebaseMessaging = initFirebaseMessaging(projectId, production.envPrefix)

    val gjirafaProject = SystemEnv.gjirafaProject
    val gjirafaApiKey = SystemEnv.gjirafaApiKey
    val gjirafaImageKey = SystemEnv.gjirafaImageKey
    val gjirafaUploadsService = GjirafaUploadsService(gjirafaProject, gjirafaApiKey, gjirafaImageKey)
    val gjirafaLivestreamService = GjirafaLivestreamsService(gjirafaProject, gjirafaApiKey)
    val gjirafaStatsService = GjirafaStatsService(gjirafaProject, gjirafaApiKey)

    val userRelationsService = UserRelationsService(
        subscribersCollection = subscribersCollection,
    )

    val postRepositoryDeprecated = PostRepositoryDeprecated(
        postsCollection = typedPostsCollection,
        messageThreadCollection = messageThreadsCollection,
        postPaymentsCollection = postPaymentsCollection,
        userRelationsService = userRelationsService,
    )

    val userRepository = UserRepository(lazyContext)
    val messageThreadService = MessageThreadService(
        messageThreadRepository = messageThreadsCollection,
        userRelationsService = userRelationsService,
        postRepository = postRepositoryDeprecated,
        userRepository = userRepository,
        lazyContext = lazyContext,
    )

    val typedCategoriesCollection = firestoreRef.typedCollectionOf(Category)

    val postRepository = PostRepository(lazyContext)
    val postServiceDeprecated = PostServiceDeprecated(
        postsCollection = typedPostsCollection,
        messageThreadService = messageThreadService,
        gjirafaService = gjirafaUploadsService,
        pubSub = pubSub,
        postPaymentCollection = postPaymentsCollection,
        postRepository = postRepository,
        firestore = firestoreRef.firestore,
        lazyContext = lazyContext,
        userRepository = userRepository,
        firebaseMessaging = firebaseMessaging,
        messageThreadsCollection = messageThreadsCollection,
        log = log,
    )

    val postService = PostService(
        typedPostsCollection,
        postRepository,
        gjirafaUploadsService,
        gjirafaLivestreamService,
        pubSub,
    )

    postRepositoryDeprecated.postServiceDeprecated = postServiceDeprecated

    val categoriesCollection = firestoreRef.typedCollectionOf(Category)
    val categoriesRepository = CategoriesRepository(categoriesCollection, typedPostsCollection)

    val fulltextSearchService = FirestoreFulltextService(
        fulltextDataSource = firestoreRef.typedCollectionOf(FulltextIndex),
    )

    val tiersCollection = firestoreRef.typedCollectionOf(Tier)
    val tiersRepository = TiersRepository(
        tiersCollection = tiersCollection,
    )

    val usersCollection = firestoreRef.typedCollectionOf(User)

    val imageRepository = ImageRepository(
        production = production,
    )

    val stripeKeysRepository = TypedCollectionReference<StripeKeys>(firestoreRef.firestore["constants"])
    val stripeKeysEu = stripeKeysRepository["${production.envPrefix}-stripe-eu"].get()
    val stripeKeysUs = stripeKeysRepository["${production.envPrefix}-stripe-us"].get()
    val stripeClients = StripeClients(
        keysEu = SystemEnv.stripeKeyEu,
        keysUs = SystemEnv.stripeKeyUs,
    )
    val stripeService = StripeService(
        clients = stripeClients,
        pubSub = pubSub,
    )
    val stripePaymentMethods = StripePaymentMethodsService(
        clients = stripeClients,
        service = stripeService,
        pubSub = pubSub,
    )
    val stripeAccountService = StripeAccountService(
        hostname = hostname,
        hostnameServices = hostnameServices,
        clients = stripeClients,
    )
    val countryToVatMapping = VatMappingProvider(systemEnv("FLEXIBEE_PASSWORD")).countryToVatMapping()

    val stripeCouponService = StripeCouponService(clients = stripeClients)

    val subscriptionService = StripeSubscriptionService(
        clients = stripeClients,
        paymentMethodsService = stripePaymentMethods,
        production = production,
        countryToVatMapping = countryToVatMapping,
        stripeCouponService = stripeCouponService,
    )

    val pathsCollection = firestoreRef.typedCollectionOf(Path)
    val usersRepository = UsersRepository(
        firestoreFulltext = fulltextSearchService,
        imageRepository = imageRepository,
        pathCollection = pathsCollection,
        pubSub = pubSub,
        subscriberCollection = subscribersCollection,
        accountService = stripeAccountService,
        collection = usersCollection,
        userIdGenerator = UserIdGenerator(),
        userRepository = userRepository,
    )

    val subscribersRepository = SubscribersRepository(
        collection = subscribersCollection,
        pubSub = pubSub,
    )

    val appleSigningService = AppleSigningService(production = production)
    val appleTransfersCollection = firestoreRef.typedCollectionOf(AppleCharge)

    val stripeSubscriberSaver = StripeSubscriberSaver(
        subscribersCollection,
        tiersCollection,
        AppleSubscriptionService(appleSigningService, production),
        pubSub,
    )

    val subscriberStripeRepository = SubscriberStripeRepository(
        stripe = stripeService,
        stripeSubscriptionService = subscriptionService,
        stripePricesCollection = firestoreRef.typedCollectionOf(StripePrice),
        subscriberRepository = subscribersRepository,
        tierRepository = tiersRepository,
        userRepository = usersRepository,
        firestore = firestoreRef.firestore,
        stripeSubscriberSaver = stripeSubscriberSaver,
        lazyContext = lazyContext,
        stripeCouponService = stripeCouponService,
    )

    val invoicesCollection = firestoreRef.typedCollectionOf(Invoice)
    val invoiceService = InvoiceService(invoicesCollection, usersCollection)

    val typedPostPaymentCollection = firestoreRef.typedCollectionOf(PostPayment)
    val stripePaymentsService = StripePaymentsService(
        tierRepository = tiersRepository,
        postPaymentsCollection = typedPostPaymentCollection,
        stripe = stripeService,
        subscriberStripeRepository = subscriberStripeRepository,
        stripeCouponService = stripeCouponService,
        pubSub = pubSub,
        hostname = hostname,
        hostnameServices = hostnameServices,
        countryToVatMapping = countryToVatMapping,
    )
    val rssFeedsRepository = RssFeedsRepository(hostnameServices, firestoreRef.typedCollectionOf(RssFeed))

    val subscriberStatisticsQueryService = SubscriberStatisticsQueryService(lazyContext)
    val userQueryService = UserQueryService(
        userRepository,
        typedCategoriesCollection,
        pathsCollection,
    )
    val usersStoresCollection = firestoreRef.typedCollectionOf(UserStore)
    val notificationSettingsRepository = NotificationSettingsRepository(lazyContext)
    val notificationRepository = NotificationRepository(lazyContext)
    http4kInJetty(
        apiInfo = "Herohero Post service",
        production = production,
        isLocalHost = isLocalHost,
        controllers = listOf(
            MessageThreadsControllerDeprecated(
                messageThreadService = messageThreadService,
            ),
            CommunitiesController(
                CommunityCommandService(CommunityRepository(lazyContext), userRepository, pubSub, lazyContext),
                CommunityQueryService(lazyContext, CommunityRepository(lazyContext)),
                userQueryService,
            ),
            MessageThreadsController(
                MessageThreadQueryService(
                    messageThreadsCollection,
                    typedPostsCollection,
                    usersCollection,
                    userRelationsService,
                ),
                MessageQueryService(
                    messageThreadsCollection,
                    typedPostsCollection,
                    postPaymentsCollection,
                ),
                MessageCommandService(
                    messageThreadsCollection,
                    postService,
                    userRepository,
                    firebaseMessaging,
                    lazyContext,
                ),
            ),
            PostsJsonApiController(
                postRepository = postRepositoryDeprecated,
                postServiceDeprecated = postServiceDeprecated,
                postQueryService = PostQueryService(typedPostsCollection, userRelationsService, postPaymentsCollection),
                usersRepository = usersRepository,
                categoriesRepository = categoriesRepository,
            ),
            PostsController(
                PostVoteCommandService(lazyContext, postRepository, typedPostsCollection),
                DeletePostCommandService(
                    lazyContext,
                    typedPostsCollection,
                    postRepository,
                    pubSub,
                    log,
                ),
                CreatorPostQueryService(
                    subscribersCollection,
                    typedCategoriesCollection,
                    savedPostsCollection,
                    postRepository,
                    lazyContext,
                ),
                CreatorPostCommandService(lazyContext, postService, typedCategoriesCollection),
                CommentQueryService(lazyContext, postRepository, subscribersCollection),
                CommentCommandService(typedPostsCollection, postService, subscribersCollection, lazyContext),
            ),
            PollsController(
                PollQueryService(lazyContext, subscribersCollection),
                PollCommandService(lazyContext, typedPostsCollection),
            ),
            LibraryController(
                LibraryCommandService(savedPostsCollection, typedPostsCollection, subscribersCollection),
                LibraryQueryService(savedPostsCollection, typedPostsCollection),
            ),
            CategoriesController(categoriesRepository),
            NotificationSettingsController(
                NotificationSettingsQueryService(notificationSettingsRepository),
                NotificationSettingsCommandService(usersCollection, notificationSettingsRepository),
            ),
            NotificationsController(
                NotificationCommandService(notificationRepository, lazyContext),
                NotificationQueryService(notificationRepository, userRepository),
            ),
            UserFactoryController(usersRepository),
            UsersJsonApiController(
                categoriesRepository,
                DeleteUserCommandService(usersCollection, SessionRepository(lazyContext), pubSub),
                usersRepository,
                pubSub,
                tiersRepository,
                rssFeedsRepository,
            ),
            FeaturedCreatorsController(FeaturedCreatorsQueryService(lazyContext, userRepository)),
            UsersController(
                userQueryService,
                SearchUsersQueryService(lazyContext),
                UserCommandService(
                    usersCollection,
                    userRepository,
                    pathsCollection,
                    pubSub,
                ),
            ),
            StatisticsController(
                subscriberStatisticsQueryService,
                PostStatisticsQueryService(typedPostsCollection, gjirafaStatsService, lazyContext),
                IncomeQueryService(usersCollection, lazyContext),
            ),
            SubscriberCallToActionController(usersCollection, pubSub, hostname),
            SubscribeRequestsController(
                SubscribeRequestCommandService(
                    subscribeRequestRepository = SubscribeRequestRepository(lazyContext),
                    usersCollection = usersCollection,
                    subscriberStripeRepository = subscriberStripeRepository,
                    stripeSubscriptionService = subscriptionService,
                    notificationRepository = notificationRepository,
                    firebaseMessaging = firebaseMessaging,
                    lazyContext = lazyContext,
                ),
                SubscribeRequestQueryService(lazyContext),
            ),
            StoresController(
                usersStoresCollection,
                UserMediaCommandService(usersStoresCollection, postRepository),
                WatchActivityCommandService(lazyContext, postRepository),
            ),
            StripeAppleController(
                AppleCallbackParser(appleSigningService),
                ApplePricingService(),
                appleSigningService,
                StripeAppleMirroringService(
                    subscriberStripeRepository,
                    stripeClients,
                    usersRepository,
                    appleTransfersCollection,
                ),
                usersRepository,
            ),
            StripeController(
                production = production,
                hostname = hostname,
                stripe = stripeService,
                stripePaymentMethods = stripePaymentMethods,
                stripePublicKeys = mapOf(
                    Currency.EUR to stripeKeysEu.publicKey,
                    Currency.USD to if (production) stripeKeysEu.publicKey else stripeKeysUs.publicKey,
                ),
                stripePaymentsService = stripePaymentsService,
                stripeAccountService = stripeAccountService,
                subscriptionService = subscriptionService,
                subscriberRepository = subscribersRepository,
                subscriberStripeRepository = subscriberStripeRepository,
                tierRepository = tiersRepository,
                userRepository = usersRepository,
                categoriesRepository = categoriesRepository,
                countryToVatMapping = countryToVatMapping,
                cancelSubscriptionCommandService = CancelSubscriptionCommandService(stripeClients),
                subscribersCollection = subscribersCollection,
                invoicesCollection = invoicesCollection,
            ),
            StripeCouponController(
                tierRepository = tiersRepository,
                stripePaymentsService = stripePaymentsService,
                stripeCouponService = stripeCouponService,
                stripeService = stripeService,
                userRepository = usersRepository,
            ),
            StripeWebhookController(
                pubSub = pubSub,
                webhookSecrets = mapOf(
                    Currency.EUR to stripeKeysEu.webhookSecrets,
                    Currency.USD to if (production) stripeKeysEu.webhookSecrets else stripeKeysUs.webhookSecrets,
                ),
            ),
            SubscribersController(
                SubscriberQueryService(subscribersCollection, usersCollection),
                SubscriberReportService(usersCollection, SystemEnv.internalApiKey),
            ),
            TiersController(tiersRepository, usersRepository),
            CountriesController(),
            InvoicesController(usersRepository, invoicesCollection, invoiceService, hostnameServices),
            AdminUsersController(userQueryService),
            DevicesController(DeviceCommandService(lazyContext)),
            LivenessController(),
        ),
    )
}

private fun lazyContext(environment: String): Lazy<DSLContext> {
    // if environment is not local, we have to trigger the lazy loading, so we know if a configuration is wrong
    // on the start
    try {
        ConnectorConnectionPool.dataSource
    } catch (e: Exception) {
        if (environment != "local") {
            throw e
        } else {
            log.warn("Failed to load data source, ignoring as run in local env: ${e.message}")
        }
    }

    return lazy {
        JooqSQL.context(ConnectorConnectionPool.dataSource)
    }
}
