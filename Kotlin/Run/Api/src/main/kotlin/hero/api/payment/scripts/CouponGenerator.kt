package hero.api.payment.scripts

import hero.baseutils.plusDays
import hero.baseutils.randomString
import hero.gcloud.typedCollectionOf
import hero.model.Currency
import hero.model.Tier
import hero.model.User
import hero.stripe.model.StripePrice
import hero.stripe.service.StripeCouponService
import java.time.Instant

fun main() {
    val isProduction = false
    val creatorId = "rumaanuceblmiy"
    val percentOff = 30
    val couponsToGenerate = 30
    val redemptions = 1
    val months = 9
    val currency = Currency.EUR
    val days = null
    val campaign = "Campaign name"
    val redeemBy = Instant.now().plusDays(30)

    val (firestore, _, _, _, clients) = initializeStripeScript(isProduction)
    val usersCollection = firestore.typedCollectionOf(User)
    val service = StripeCouponService(clients)

    val creator = usersCollection[creatorId].get()
    val tier = Tier.ofId(creator.creator.tierId)

    val stripePricesCollection = firestore.typedCollectionOf(StripePrice)
    val stripePriceId = stripePricesCollection["${creator.id}|${creator.creator.tierId}"].get().stripeId

    fun generateCouponCode(): String {
        while (true) {
            val couponId = randomString(10).uppercase()
            // check if coupon with given id exists (in both Stripe accounts) and return its value if not
            service.getCouponOrNull("$creatorId-$couponId", tier.currency)
                ?: return couponId
        }
    }

    (1..couponsToGenerate).toList()
        .parallelStream()
        .forEach {
            val price = clients[currency].prices().retrieve(stripePriceId)
            val coupon = service.createCoupon(
                purchasedByUserId = creatorId,
                couponCode = generateCouponCode(),
                creatorId = creator.id,
                tier = tier,
                price = price,
                percentOff = percentOff,
                months = months,
                days = days,
                redeemBy = redeemBy,
                redemptions = redemptions,
                campaign = campaign,
                currency = tier.currency,
            )
            println(coupon.metadata["couponCode"])
        }
}
