package hero.functions

import hero.model.topics.WriteSubscriberStatisticsRequested
import hero.sql.jooq.tables.DailySubscriberStatistics.DAILY_SUBSCRIBER_STATISTICS
import hero.test.IntegrationTest
import hero.test.TestEnvironmentVariables
import hero.test.logging.TestLogger
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.time.Month
import java.util.stream.Collectors

class SubscriberStatisticsWriterIT : IntegrationTest() {
    @Test
    fun `should aggregate data from subscription table and aggregate them into daily stats table - only in the past`() {
        val underTest = SubscriberStatisticsWriter(lazyTestContext, TestLogger, TestEnvironmentVariables)
        testHelper.createUser("cestmirstrakatyheroherorpkjaolu")
        testHelper.createUser("subscriber")
        testContext.execute(
            """
insert into subscription (stripe_id, customer_id, creator_id, started_at, ended_at, ends_at, cancelled_at, status, currency, user_id, creator_country, tier_id, price_cents, created_at, updated_at)
values  ('sub_1NTgC0B6ZCHekl2R9S87n4Me', 'cus_OGCbcBaLDjJnBD', 'cestmirstrakatyheroherorpkjaolu', '2024-02-28 08:13:32.000000', '2024-03-01 08:13:32.000000', '2024-03-01 08:13:32.000000', '2024-02-29 23:02:48.000000', 'canceled', 'EUR', 'subscriber', null, 'EUR05', 500, '2024-01-14 08:14:04.866699', '2024-03-07 23:02:49.486003'),
        ('sub_1MCVHQB6ZCHekl2RjQSA5nzU', 'cus_MwO3SRr4xCNxD8', 'cestmirstrakatyheroherorpkjaolu', '2024-02-29 23:35:51.000000', '2024-03-02 23:35:51.000000', '2024-03-02 23:35:51.000000', '2024-03-01 23:02:48.000000', 'canceled', 'EUR', 'subscriber', null, 'EUR05', 500, '2024-01-07 22:36:40.346820', '2024-03-07 22:35:55.138720'),
        ('sub_1MYwQ4B6ZCHekl2R8bT6ww8i', 'cus_NJZZ1CQHL1MTKR', 'cestmirstrakatyheroherorpkjaolu', '2024-03-02 19:01:31.000000', '2024-03-04 19:01:31.000000', '2024-03-03 23:02:48.000000', '2024-03-03 19:01:31.000000', 'past_due', 'EUR', 'subscriber', null, 'EUR05', 500, '2024-01-07 19:02:05.711524', '2024-03-07 19:01:57.288599'),
        ('sub_1OW1ewB6ZCHekl2RCuGkGTT5', 'cus_PKh2pA0jjaIkMN', 'cestmirstrakatyheroherorpkjaolu', '2024-03-03 19:05:22.000000', '2024-03-05 19:05:22.000000', '2024-03-05 19:05:22.000000', '2024-03-04 23:02:48.000000', 'canceled', 'EUR', 'subscriber', null, 'EUR06', 600, '2024-01-07 19:05:26.312449', '2024-03-07 19:06:24.839019'),
        ('sub_1OW44yB6ZCHekl2RajEtiLi9', 'cus_Om5b2BvT79WJQx', 'cestmirstrakatyheroherorpkjaolu', '2024-03-03 23:40:24.000000', '2024-03-05 23:40:24.000000', '2024-03-05 23:40:24.000000', '2024-03-04 22:32:45.000000', 'canceled', 'EUR', 'subscriber', null, 'EUR06', 600, '2024-01-07 22:40:28.223641', '2024-03-07 22:41:24.612990'),
        ('sub_1Mj7BhB6ZCHekl2RgbWC7ZsC', 'cus_NGpb0Hw8K3XyTa', 'cestmirstrakatyheroherorpkjaolu', '2024-03-04 22:32:45.000000', '2024-03-06 22:32:45.000000', '2024-03-06 22:32:45.000000', '2024-03-05 22:32:45.000000', 'past_due', 'EUR', 'subscriber', null, 'EUR05', 500, '2024-01-07 21:33:40.670336', '2024-03-07 22:33:37.000699'),
        ('sub_1MNjyHB6ZCHekl2R8B8DYBDY', 'cus_MWJlIDQQ5LD1ul', 'cestmirstrakatyheroherorpkjaolu', '2024-03-05 23:30:33.000000', '2024-03-07 23:30:33.000000', '2024-03-07 23:30:33.000000', '2024-03-06 22:32:45.000000', 'canceled', 'EUR', 'subscriber', null, 'EUR05', 500, '2024-01-07 22:31:01.933060', '2024-03-07 22:30:49.884300'),
        -- this row should be ignored since it's so far in the future
        ('sub_1MAYKXB6ZCHekl2RqoVTI1Vs', 'cus_IzyP8Dy2QlmsOH', 'cestmirstrakatyheroherorpkjaolu', '2024-12-29 12:27:01.000000', '2025-01-01 17:13:06.000000', '2025-01-02 12:27:01.000000', '2025-03-01 17:13:06.000000', 'canceled', 'EUR', 'subscriber', null, 'EUR05', 500, '2025-02-02 12:27:45.859439', '2025-03-07 17:13:16.475022');
            """.trimIndent(),
        )

        testContext.execute(
            """
insert into charge (stripe_id, customer_id, amount, amount_captured, amount_refunded, payment_intent_id, status, stripe_created_at, payment_method_id, succeeded_charges, currency, refunded, disputed, creator_id, transfer_amount)
values  ('ch_3Lpv3bB6ZCHekl2R0loMdDye', 'cus_MZ39E6SytiH5Uw', 500, 500, 0, 'pi_3Lpv3bB6ZCHekl2R0YCp8eNX', 'succeeded', '2024-02-28 08:13:32.000000', 'pm_1Lpv3WB6ZCHekl2Rro9MR5PF', 1, 'EUR', false, false, 'cestmirstrakatyheroherorpkjaolu', 450),
        ('ch_3LskBXB6ZCHekl2R1CIU4i0Y', 'cus_JmUUfHoUoT5L97', 500, 500, 0, 'pi_3LskBXB6ZCHekl2R1HTp2hBb', 'succeeded', '2024-02-28 08:13:32.000000', 'pm_1LskBUB6ZCHekl2RiMPjBbaS', 1, 'EUR', false, false, 'cestmirstrakatyheroherorpkjaolu', 450),
        -- this charge is failed, should be ignored
        ('ch_3LpxwZB6ZCHekl2R1Ne82nOU', 'cus_MZ68Mn4b0FpSkP', 500, 0, 0, 'pi_3LpxwZB6ZCHekl2R19QnuIvs', 'failed', '2024-02-29 08:13:32.000000', 'pm_1LpxwUB6ZCHekl2RxFaBiljQ', 1, 'EUR', false, false, 'cestmirstrakatyheroherorpkjaolu', 450),
        ('ch_3Lpsp7B6ZCHekl2R0QiqlGKt', 'cus_LRh6cxQgpBsLRj', 500, 500, 0, 'pi_3Lpsp7B6ZCHekl2R0c0VUigj', 'succeeded', '2024-02-29 08:13:32.000000', 'pm_1KknikB6ZCHekl2Re3AwB4IR', 1, 'EUR', false, false, 'cestmirstrakatyheroherorpkjaolu', 450);
            """.trimIndent(),
        )

        underTest.consume(
            WriteSubscriberStatisticsRequested(
                setOf("cestmirstrakatyheroherorpkjaolu"),
                LocalDate.of(2024, 3, 7),
            ),
        )

        val result = testContext
            .select(
                DAILY_SUBSCRIBER_STATISTICS.DATE,
                DAILY_SUBSCRIBER_STATISTICS.CREATOR_ID,
                DAILY_SUBSCRIBER_STATISTICS.TOTAL_SUBSCRIBERS,
                DAILY_SUBSCRIBER_STATISTICS.ACTIVE_SUBSCRIBERS,
                DAILY_SUBSCRIBER_STATISTICS.SUBSCRIBED,
                DAILY_SUBSCRIBER_STATISTICS.UNSUBSCRIBED,
                DAILY_SUBSCRIBER_STATISTICS.TOTAL_INCOME_CENTS,
            )
            .from(DAILY_SUBSCRIBER_STATISTICS)
            .orderBy(DAILY_SUBSCRIBER_STATISTICS.DATE)
            .fetch()
            .map {
                TestDailySubscriberStatistics(
                    date = it.value1(),
                    creatorId = it.value2(),
                    totalSubscribers = it.value3(),
                    activeSubscribers = it.value4(),
                    subscribed = it.value5(),
                    unsubscribed = it.value6(),
                    totalIncome = it.value7(),
                )
            }
        val creatorId = "cestmirstrakatyheroherorpkjaolu"
        val zeroStatsDays = LocalDate.of(2020, Month.DECEMBER, 10).datesUntil(LocalDate.parse("2024-02-28"))
            .map {
                TestDailySubscriberStatistics(it, creatorId, 0, 0, 0, 0, 0)
            }
            .collect(Collectors.toList())

        // started at - ended at - cancelled at
        // 28.02 - 01.03 - 29.02
        // 29.02 - 02.03 - 01.03
        // 02.03 - 04.03 - 03.03
        // 03.03 - 05.03 - 04.03
        // 03.03 - 05.03 - 04.03
        // 04.03 - 06.03 - 05.03
        // 05.03 - 07.03 - 06.03
        val expectedStatsDays = zeroStatsDays + listOf(
            TestDailySubscriberStatistics(LocalDate.parse("2024-02-28"), creatorId, 1, 1, 1, 0, 900),
            TestDailySubscriberStatistics(LocalDate.parse("2024-02-29"), creatorId, 2, 1, 1, 1, 450),
            TestDailySubscriberStatistics(LocalDate.parse("2024-03-01"), creatorId, 2, 0, 0, 1, 0),
            TestDailySubscriberStatistics(LocalDate.parse("2024-03-02"), creatorId, 2, 1, 1, 0, 0),
            TestDailySubscriberStatistics(LocalDate.parse("2024-03-03"), creatorId, 3, 2, 2, 1, 0),
            TestDailySubscriberStatistics(LocalDate.parse("2024-03-04"), creatorId, 4, 1, 1, 2, 0),
            TestDailySubscriberStatistics(LocalDate.parse("2024-03-05"), creatorId, 4, 1, 1, 1, 0),
            TestDailySubscriberStatistics(LocalDate.parse("2024-03-06"), creatorId, 2, 0, 0, 1, 0),
            TestDailySubscriberStatistics(LocalDate.parse("2024-03-07"), creatorId, 1, 0, 0, 0, 0),
        )

        assertThat(result).isEqualTo(expectedStatsDays)
    }

    @Test
    fun `should aggregate data from subscription table and aggregate them into daily stats table - past and present`() {
        val underTest = SubscriberStatisticsWriter(lazyTestContext, TestLogger, TestEnvironmentVariables)
        testHelper.createUser("cestmirstrakatyheroherorpkjaolu")
        testHelper.createUser("subscriber")
        testContext.execute(
            """
insert into subscription (stripe_id, customer_id, creator_id, started_at, ended_at, ends_at, cancelled_at, status, currency, user_id, creator_country, tier_id, price_cents, created_at, updated_at)
values  ('sub_1NTgC0B6ZCHekl2R9S87n4Me', 'cus_OGCbcBaLDjJnBD', 'cestmirstrakatyheroherorpkjaolu', now() - '8 days'::interval, now() - '3 days'::interval, now() - '3 days'::interval, now() - '4 days'::interval, 'canceled', 'EUR', 'subscriber', null, 'EUR05', 500, '2024-01-14 08:14:04.866699', '2024-03-07 23:02:49.486003'),
        ('sub_1MCVHQB6ZCHekl2RjQSA5nzU', 'cus_MwO3SRr4xCNxD8', 'cestmirstrakatyheroherorpkjaolu', now() - '7 days'::interval, now() - '2 days'::interval, now() - '2 days'::interval, now() - '3 days'::interval, 'canceled', 'EUR', 'subscriber', null, 'EUR05', 500, '2024-01-07 22:36:40.346820', '2024-03-07 22:35:55.138720'),
        ('sub_1MYwQ4B6ZCHekl2R8bT6ww8i', 'cus_NJZZ1CQHL1MTKR', 'cestmirstrakatyheroherorpkjaolu', now() - '5 days'::interval, now() - '0 days'::interval, now() - '0 days'::interval, now() - '1 days'::interval, 'past_due', 'EUR', 'subscriber', null, 'EUR05', 500, '2024-01-07 19:02:05.711524', '2024-03-07 19:01:57.288599'),
        ('sub_1OW1ewB6ZCHekl2RCuGkGTT5', 'cus_PKh2pA0jjaIkMN', 'cestmirstrakatyheroherorpkjaolu', now() - '4 days'::interval, null, now() + '2 days'::interval, null, 'active', 'EUR', 'subscriber', null, 'EUR06', 600, '2024-01-07 19:05:26.312449', '2024-03-07 19:06:24.839019'),
        ('sub_1OW44yB6ZCHekl2RajEtiLi9', 'cus_Om5b2BvT79WJQx', 'cestmirstrakatyheroherorpkjaolu', now() - '4 days'::interval, null, now() + '2 days'::interval, null, 'active', 'EUR', 'subscriber', null, 'EUR06', 600, '2024-01-07 22:40:28.223641', '2024-03-07 22:41:24.612990'),
        ('sub_1Mj7BhB6ZCHekl2RgbWC7ZsC', 'cus_NGpb0Hw8K3XyTa', 'cestmirstrakatyheroherorpkjaolu', now() - '3 days'::interval, null, now() + '2 days'::interval, null, 'active', 'EUR', 'subscriber', null, 'EUR05', 500, '2024-01-07 21:33:40.670336', '2024-03-07 22:33:37.000699'),
        ('sub_1MNjyHB6ZCHekl2R8B8DYBDY', 'cus_MWJlIDQQ5LD1ul', 'cestmirstrakatyheroherorpkjaolu', now() - '2 days'::interval, null, now() + '2 days'::interval, null, 'past_due', 'EUR', 'subscriber', null, 'EUR05', 500, '2024-01-07 22:31:01.933060', '2024-03-07 22:30:49.884300'),
        -- this row should be ignored since it's so far in the future
        ('sub_1MAYKXB6ZCHekl2RqoVTI1Vs', 'cus_IzyP8Dy2QlmsOH', 'cestmirstrakatyheroherorpkjaolu', '2028-12-29 12:27:01.000000', '2029-01-01 17:13:06.000000', '2029-01-02 12:27:01.000000', '2029-03-01 17:13:06.000000', 'canceled', 'EUR', 'subscriber', null, 'EUR05', 500, '2029-02-02 12:27:45.859439', '2029-03-07 17:13:16.475022');
            """.trimIndent(),
        )
        testContext.execute(
            """
                insert into daily_subscriber_statistics (date, creator_id, total_subscribers, active_subscribers, subscribed, unsubscribed, total_income_cents)
                values  (now() - '9 days'::interval, 'cestmirstrakatyheroherorpkjaolu', 0, 0, 0, 0, 0);
            """.trimIndent(),
        )

        val now = LocalDate.now()
        underTest.consume(
            WriteSubscriberStatisticsRequested(
                setOf("cestmirstrakatyheroherorpkjaolu"),
                now,
            ),
        )

        val result = testContext
            .select(
                DAILY_SUBSCRIBER_STATISTICS.DATE,
                DAILY_SUBSCRIBER_STATISTICS.CREATOR_ID,
                DAILY_SUBSCRIBER_STATISTICS.TOTAL_SUBSCRIBERS,
                DAILY_SUBSCRIBER_STATISTICS.ACTIVE_SUBSCRIBERS,
                DAILY_SUBSCRIBER_STATISTICS.SUBSCRIBED,
                DAILY_SUBSCRIBER_STATISTICS.UNSUBSCRIBED,
                DAILY_SUBSCRIBER_STATISTICS.TOTAL_INCOME_CENTS,
            )
            .from(DAILY_SUBSCRIBER_STATISTICS)
            .orderBy(DAILY_SUBSCRIBER_STATISTICS.DATE)
            .fetch()
            .map {
                TestDailySubscriberStatistics(
                    date = it.value1(),
                    creatorId = it.value2(),
                    totalSubscribers = it.value3(),
                    activeSubscribers = it.value4(),
                    subscribed = it.value5(),
                    unsubscribed = it.value6(),
                    totalIncome = it.value7(),
                )
            }
        val creatorId = "cestmirstrakatyheroherorpkjaolu"

        // started at, ended at, cancelled at
        // -8 days, -3 days, -4 days
        // -7 days, -2 days, -3 days
        // -5 days, -0 days, -1 days
        // -4 days, null, null
        // -4 days, null, null
        // -3 days, null, null
        // -2 days, null, null
        val expectedStatsDays = listOf(
            TestDailySubscriberStatistics(now.minusDays(9), creatorId, 0, 0, 0, 0, 0),
            TestDailySubscriberStatistics(now.minusDays(8), creatorId, 1, 1, 1, 0, 0),
            TestDailySubscriberStatistics(now.minusDays(7), creatorId, 2, 2, 1, 0, 0),
            TestDailySubscriberStatistics(now.minusDays(6), creatorId, 2, 2, 0, 0, 0),
            TestDailySubscriberStatistics(now.minusDays(5), creatorId, 3, 3, 1, 0, 0),
            TestDailySubscriberStatistics(now.minusDays(4), creatorId, 5, 4, 2, 1, 0),
            TestDailySubscriberStatistics(now.minusDays(3), creatorId, 6, 4, 1, 1, 0),
            TestDailySubscriberStatistics(now.minusDays(2), creatorId, 6, 5, 1, 0, 0),
            TestDailySubscriberStatistics(now.minusDays(1), creatorId, 5, 4, 0, 1, 0),
            TestDailySubscriberStatistics(now.minusDays(0), creatorId, 5, 4, 0, 0, 0),
        )

        assertThat(result).isEqualTo(expectedStatsDays)
    }

    @Test
    fun `should add only a single new row for next day`() {
        val underTest = SubscriberStatisticsWriter(lazyTestContext, TestLogger, TestEnvironmentVariables)
        testHelper.createUser("cestmirstrakatyheroherorpkjaolu")
        testHelper.createUser("subscriber")
        testContext.execute(
            """
insert into subscription (stripe_id, customer_id, creator_id, started_at, ended_at, ends_at, status, currency, user_id, creator_country, tier_id, price_cents, created_at, updated_at, cancelled_at, cancellation_reason)
values  ('sub_1NTgC0B6ZCHekl2R9S87n4Me', 'cus_OGCbcBaLDjJnBD', 'cestmirstrakatyheroherorpkjaolu', '2024-02-28 08:13:32.000000', null, '2024-03-01 08:13:32.000000', 'canceled', 'EUR', 'subscriber', null, 'EUR05', 500, '2024-01-14 08:14:04.866699', '2024-03-07 23:02:49.486003', '2024-03-07 23:02:48.000000', 'cancellation_requested'),
        ('sub_1MCVHQB6ZCHekl2RjQSA5nzU', 'cus_MwO3SRr4xCNxD8', 'cestmirstrakatyheroherorpkjaolu', '2024-02-29 23:35:51.000000', null, '2024-03-02 23:35:51.000000', 'canceled', 'EUR', 'subscriber', null, 'EUR05', 500, '2024-01-07 22:36:40.346820', '2024-03-07 22:35:55.138720', null, null),
        ('sub_1MYwQ4B6ZCHekl2R8bT6ww8i', 'cus_NJZZ1CQHL1MTKR', 'cestmirstrakatyheroherorpkjaolu', '2024-03-02 19:01:31.000000', '2024-03-03 19:01:31.000000', '2024-03-04 19:01:31.000000', 'past_due', 'EUR', 'subscriber', null, 'EUR05', 500, '2024-01-07 19:02:05.711524', '2024-03-07 19:01:57.288599', '2024-03-03 06:04:59.000000', 'cancellation_requested'),
        ('sub_1OW1ewB6ZCHekl2RCuGkGTT5', 'cus_PKh2pA0jjaIkMN', 'cestmirstrakatyheroherorpkjaolu', '2024-03-03 19:05:22.000000', '2024-03-04 19:05:22.000000', '2024-03-05 19:05:22.000000', 'canceled', 'EUR', 'subscriber', null, 'EUR06', 600, '2024-01-07 19:05:26.312449', '2024-03-07 19:06:24.839019', '2024-03-04 15:40:04.000000', 'cancellation_requested'),
        ('sub_1OW44yB6ZCHekl2RajEtiLi9', 'cus_Om5b2BvT79WJQx', 'cestmirstrakatyheroherorpkjaolu', '2024-03-03 23:40:24.000000', null, '2024-03-05 23:40:24.000000', 'canceled', 'EUR', 'subscriber', null, 'EUR06', 600, '2024-01-07 22:40:28.223641', '2024-03-07 22:41:24.612990', null, null),
        ('sub_1Mj7BhB6ZCHekl2RgbWC7ZsC', 'cus_NGpb0Hw8K3XyTa', 'cestmirstrakatyheroherorpkjaolu', '2024-03-04 22:32:45.000000', null, '2024-03-06 22:32:45.000000', 'past_due', 'EUR', 'subscriber', null, 'EUR05', 500, '2024-01-07 21:33:40.670336', '2024-03-07 22:33:37.000699', null, null),
        ('sub_1MNjyHB6ZCHekl2R8B8DYBDY', 'cus_MWJlIDQQ5LD1ul', 'cestmirstrakatyheroherorpkjaolu', '2024-03-05 23:30:33.000000', null, '2024-03-07 23:30:33.000000', 'past_due', 'EUR', 'subscriber', null, 'EUR05', 500, '2024-01-07 22:31:01.933060', '2024-03-07 22:30:49.884300', null, null),
        -- this row should be ignored since it's so far in the future
        ('sub_1MAYKXB6ZCHekl2RqoVTI1Vs', 'cus_IzyP8Dy2QlmsOH', 'cestmirstrakatyheroherorpkjaolu', '2024-12-29 12:27:01.000000', '2025-01-01 17:13:06.000000', '2025-01-02 12:27:01.000000', 'canceled', 'EUR', 'subscriber', null, 'EUR05', 500, '2025-02-02 12:27:45.859439', '2025-03-07 17:13:16.475022', '2025-03-01 17:13:06.000000', 'payment_failed');
            """.trimIndent(),
        )
        testContext.execute(
            """
                insert into daily_subscriber_statistics (date, creator_id, total_subscribers, active_subscribers, subscribed, unsubscribed, total_income_cents)
                values  ('2024-03-06', 'cestmirstrakatyheroherorpkjaolu', 2, 1, 0, 0, 1000);
            """.trimIndent(),
        )

        underTest.consume(
            WriteSubscriberStatisticsRequested(
                setOf("cestmirstrakatyheroherorpkjaolu"),
                LocalDate.of(2024, 3, 7),
            ),
        )

        val result = testContext
            .select(
                DAILY_SUBSCRIBER_STATISTICS.DATE,
                DAILY_SUBSCRIBER_STATISTICS.CREATOR_ID,
                DAILY_SUBSCRIBER_STATISTICS.TOTAL_SUBSCRIBERS,
                DAILY_SUBSCRIBER_STATISTICS.ACTIVE_SUBSCRIBERS,
                DAILY_SUBSCRIBER_STATISTICS.SUBSCRIBED,
                DAILY_SUBSCRIBER_STATISTICS.UNSUBSCRIBED,
                DAILY_SUBSCRIBER_STATISTICS.TOTAL_INCOME_CENTS,
            )
            .from(DAILY_SUBSCRIBER_STATISTICS)
            .orderBy(DAILY_SUBSCRIBER_STATISTICS.DATE)
            .fetch()
            .map {
                TestDailySubscriberStatistics(
                    date = it.value1(),
                    creatorId = it.value2(),
                    totalSubscribers = it.value3(),
                    activeSubscribers = it.value4(),
                    subscribed = it.value5(),
                    unsubscribed = it.value6(),
                    totalIncome = it.value7(),
                )
            }

        val creatorId = "cestmirstrakatyheroherorpkjaolu"
        assertThat(result).containsExactly(
            TestDailySubscriberStatistics(LocalDate.parse("2024-03-06"), creatorId, 2, 1, 0, 0, 1000),
            TestDailySubscriberStatistics(LocalDate.parse("2024-03-07"), creatorId, 1, 1, 0, 0, 0),
        )
    }

    @Test
    fun `should not insert any data if until is less than or equal to max date from daily stats table`() {
        val underTest = SubscriberStatisticsWriter(lazyTestContext, TestLogger, TestEnvironmentVariables)
        testContext.execute(
            """
                insert into daily_subscriber_statistics (date, creator_id, total_subscribers, active_subscribers, subscribed, unsubscribed, total_income_cents)
                values  ('2024-03-13', 'cestmirstrakatyheroherorpkjaolu', 8314, 7892, 31, 33, 4528100);
            """.trimIndent(),
        )

        underTest.consume(
            WriteSubscriberStatisticsRequested(
                setOf("cestmirstrakatyheroherorpkjaolu"),
                LocalDate.of(2024, 3, 13),
            ),
        )

        val result = testContext
            .select(
                DAILY_SUBSCRIBER_STATISTICS.DATE,
                DAILY_SUBSCRIBER_STATISTICS.CREATOR_ID,
                DAILY_SUBSCRIBER_STATISTICS.TOTAL_SUBSCRIBERS,
                DAILY_SUBSCRIBER_STATISTICS.ACTIVE_SUBSCRIBERS,
                DAILY_SUBSCRIBER_STATISTICS.SUBSCRIBED,
                DAILY_SUBSCRIBER_STATISTICS.UNSUBSCRIBED,
                DAILY_SUBSCRIBER_STATISTICS.TOTAL_INCOME_CENTS,
            )
            .from(DAILY_SUBSCRIBER_STATISTICS)
            .orderBy(DAILY_SUBSCRIBER_STATISTICS.DATE)
            .fetch()
            .map {
                TestDailySubscriberStatistics(
                    it.value1(),
                    it.value2(),
                    it.value3(),
                    it.value4(),
                    it.value5(),
                    it.value6(),
                    it.value7(),
                )
            }

        val creatorId = "cestmirstrakatyheroherorpkjaolu"
        assertThat(result).containsExactly(
            TestDailySubscriberStatistics(LocalDate.parse("2024-03-13"), creatorId, 8314, 7892, 31, 33, 4528100),
        )
    }
}

private data class TestDailySubscriberStatistics(
    val date: LocalDate,
    val creatorId: String,
    val totalSubscribers: Int,
    val activeSubscribers: Int,
    val subscribed: Int,
    val unsubscribed: Int,
    val totalIncome: Int,
)
