package hero.functions

import hero.model.topics.Daily
import hero.model.topics.WriteSubscriberStatisticsRequested
import hero.test.IntegrationTest
import hero.test.TestEnvironmentVariables
import hero.test.logging.TestLogger
import io.mockk.verify
import org.junit.jupiter.api.Test
import java.time.LocalDate

class DailyActiveCreatorProcessorIT : IntegrationTest() {
    @Test
    fun `should fetch active creators and publish request to write daily stats for them`() {
        val underTest = DailyActiveCreatorProcessor(lazyTestContext, pubSubMock, TestLogger, TestEnvironmentVariables)
        listOf(
            "cung",
            "cestmir",
            "filip",
            "pepa",
            "johnmarianek",
            "subscriber",
        ).forEach {
            testHelper.createUser(it)
        }
        testContext.execute(
            """
insert into subscription (stripe_id, customer_id, creator_id, started_at, ends_at, status, currency, user_id, creator_country, tier_id, price_cents, created_at, updated_at, cancelled_at, ended_at, cancellation_reason)
values  ('sub_1MmeRmB6ZCHekl2RyKbVBado', 'cus_NXjurDXjGBJjCd', 'cung', now() - '5 day'::interval , now() + '1 day'::interval, 'active', 'EUR', 'subscriber', null, 'EUR05', 500, '2024-01-17 14:40:10.670571', '2024-03-08 15:09:00.577723', now(), null, 'cancellation_requested'),
        ('sub_1MClhxB6ZCHekl2RQh5hQXDS', 'cus_J0gOkJpN1jWOD0', 'cestmir', now() - '4 day'::interval,  now() + '2 day'::interval, 'incomplete', 'EUR', 'subscriber', null, 'EUR05', 500, '2024-01-08 15:09:09.026134', '2024-03-08 15:08:42.530235', null, null, null),
        ('sub_1Os51rB6ZCHekl2RkOZdyUQO', 'cus_PhTzE3MsnCxJ56', 'filip', now() - '3 day'::interval,  now() + '3 day'::interval, 'past_due', 'EUR', 'subscriber', null, 'EUR06', 600, '2024-03-08 15:08:16.306886', '2024-03-08 15:08:16.306886', null, null, null),
        ('sub_1Os50iB6ZCHekl2RBtkcLuPD', 'cus_PhTyOxFkf4lZOo', 'pepa', now() - '2 day'::interval,  now() + '4 day'::interval, 'active', 'EUR', 'subscriber', null, 'EUR06', 600, '2024-03-08 15:07:05.488011', '2024-03-08 15:07:05.488011', null, null, null),
        ('sub_1OVBznB6ZCHekl2RgyFBF2Cs', 'cus_PJpexevXkRRgAo', 'johnmarianek', now() - '1 day'::interval,  now() + '5 day'::interval, 'active', 'EUR', 'subscriber', null, 'EUR06', 600, '2024-01-05 17:23:16.644031', '2024-03-08 15:04:50.163454', null, null, null);
            """.trimIndent(),
        )

        underTest.consume(Daily())

        verify {
            pubSubMock.publish<WriteSubscriberStatisticsRequested>(
                match {
                    it.creatorIds == setOf("cung", "cestmir", "filip", "pepa", "johnmarianek") &&
                        it.until == LocalDate.now().minusDays(1)
                },
            )
        }
    }
}
