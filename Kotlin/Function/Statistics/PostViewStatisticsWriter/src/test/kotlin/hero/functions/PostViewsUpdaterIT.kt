package hero.functions

import hero.sql.jooq.Tables
import hero.test.IntegrationTest
import hero.test.TestRepositories
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.LocalDate

class PostViewsUpdaterIT : IntegrationTest() {
    @Test
    fun `should add views to posts from given day`() {
        val underTest = PostViewsUpdater(lazyTestContext)

        val now = LocalDate.now()
        val post1 = testHelper.createPost("cestmir", views = 50, text = "first-post")
        testHelper.createDailyPostViewsStats(post1, 200, now)
        testHelper.createDailyPostViewsStats(post1, 300, now.minusDays(1))

        // this post will not be updated since the stats are from yesterday
        val post2 = testHelper.createPost("cestmir", views = 100, text = "second-post")
        testHelper.createDailyPostViewsStats(post2, 300, now.minusDays(1))

        val post3 = testHelper.createPost("cestmir", views = 60, text = "third-post")
        testHelper.createDailyPostViewsStats(post3, 900, now)

        underTest.execute(UpdatePostViews(now))

        val posts = TestRepositories.postRepository.find { this.orderBy(Tables.POST.VIEWS.desc()) }

        assertThat(posts).containsExactly(
            post3.copy(views = 960),
            post1.copy(views = 250),
            post2.copy(views = 100),
        )
    }
}
