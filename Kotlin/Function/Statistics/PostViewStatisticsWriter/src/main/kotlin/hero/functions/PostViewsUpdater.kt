package hero.functions

import hero.sql.jooq.Tables.DAILY_POST_VIEW_STATISTICS
import hero.sql.jooq.Tables.POST
import org.jooq.DSLContext
import org.jooq.impl.DSL
import java.time.LocalDate

class PostViewsUpdater(lazyContext: Lazy<DSLContext>) {
    private val context by lazyContext

    fun execute(command: UpdatePostViews) {
        val statsCte = DSL.name("date_stats")
            .`as`(
                DSL
                    .select(DAILY_POST_VIEW_STATISTICS.POST_ID, DAILY_POST_VIEW_STATISTICS.VIEWS)
                    .from(DAILY_POST_VIEW_STATISTICS)
                    .where(DAILY_POST_VIEW_STATISTICS.DATE.eq(command.date)),
            )

        context
            .with(statsCte)
            .update(POST)
            .set(POST.VIEWS, POST.VIEWS.plus(statsCte.field(DAILY_POST_VIEW_STATISTICS.VIEWS)))
            .from(statsCte)
            .where(POST.ID.eq(statsCte.field(DAILY_POST_VIEW_STATISTICS.POST_ID)))
            .execute()
    }
}

data class UpdatePostViews(val date: LocalDate)
