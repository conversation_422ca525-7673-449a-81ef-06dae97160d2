package hero.functions

import com.stripe.exception.InvalidRequestException
import com.stripe.model.PaymentMethod
import com.stripe.param.CustomerUpdateParams
import com.stripe.param.PaymentMethodUpdateParams
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.exceptions.http.NotFoundException
import hero.model.Currency
import hero.model.topics.CardCreateType
import hero.model.topics.PaymentMethodChanged
import hero.stripe.service.StripeClients
import hero.stripe.service.StripeService
import hero.stripe.service.stripeRetry

@Suppress("unused")
class StripeChangedPaymentMethodHandler : PubSubSubscriber<PaymentMethodChanged>() {
    /** Stripe message when consumer is invoked prematurely, it will be invoked again from Stripe Webhook. */
    private val notReadyMessage = "You must save this PaymentMethod to a customer before you can update it"
    private val stripeClients = StripeClients(
        keysEu = SystemEnv.stripeKeyEu,
        keysUs = SystemEnv.stripeKeyUs,
    )
    private val stripeService: StripeService = StripeService(stripeClients, null)

    override fun consume(payload: PaymentMethodChanged) {
        val paymentMethod = stripeRetry {
            stripeClients[payload.currency].paymentMethods().retrieve(payload.paymentMethodId)
        } ?: throw NotFoundException("PaymentMethod ${payload.paymentMethodId} not found.")

        updateCurrencyAndCardCreateType(payload.customerId, paymentMethod, payload.cardCreateType, payload.currency)

        if (payload.makeDefault) {
            makePaymentMethodDefault(payload.customerId, payload.paymentMethodId, payload.currency)
        }
    }

    private fun makePaymentMethodDefault(
        customerId: String,
        paymentMethodId: String?,
        currency: Currency,
    ) {
        stripeRetry {
            val customer = stripeRetry {
                stripeClients[currency].customers().retrieve(customerId)
            }
            log.info(
                "Changing paymentMethod from ${customer.invoiceSettings?.defaultPaymentMethod} " +
                    "to $paymentMethodId for customer $customerId.",
                mapOf("customerId" to customerId),
            )
            val params = CustomerUpdateParams
                .builder()
                .setInvoiceSettings(
                    CustomerUpdateParams.InvoiceSettings.builder()
                        .setDefaultPaymentMethod(paymentMethodId)
                        .build(),
                )
                .build()
            try {
                stripeClients[currency].customers().update(customer.id, params)
            } catch (e: InvalidRequestException) {
                if (notReadyMessage in (e.userMessage ?: e.message ?: "")) {
                    log.info("Payment method $paymentMethodId of $customerId is not yet ready.")
                    return@stripeRetry
                }
                log.error("Cannot make payment $paymentMethodId to be default for $customerId: ${e.message}")
            }
        }
    }

    private fun updateCurrencyAndCardCreateType(
        customerId: String,
        paymentMethod: PaymentMethod,
        cardCreateType: CardCreateType?,
        currency: Currency,
    ) {
        stripeRetry {
            val params = PaymentMethodUpdateParams.builder()
                .putMetadata("currency", currency.name)
            if (cardCreateType != null) {
                params.putMetadata("cardCreateType", cardCreateType.name)
            }
            try {
                paymentMethod.update(params.build())
            } catch (e: InvalidRequestException) {
                if (notReadyMessage in (e.userMessage ?: e.message ?: "")) {
                    log.info(
                        "Payment method ${paymentMethod.id} of $customerId is not yet ready.",
                    )
                    return@stripeRetry
                }
                throw e
            }
        }
    }
}
