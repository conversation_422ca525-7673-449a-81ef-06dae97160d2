package hero.functions

import com.stripe.StripeClient
import com.stripe.model.Transfer
import com.stripe.param.DisputeListParams
import com.stripe.param.RefundListParams
import com.stripe.param.TransferReversalCollectionCreateParams
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.baseutils.minusDays
import hero.baseutils.minusHours
import hero.gcloud.FirestoreRef
import hero.gcloud.TypedCollectionReference
import hero.gcloud.firestore
import hero.gcloud.get
import hero.model.Currency
import hero.model.topics.Hourly
import hero.stripe.model.StripeKeys
import hero.stripe.service.StripeClients
import java.time.Instant

@Suppress("unused")
class StripeRefundTransferReverser(
    production: Boolean = SystemEnv.isProduction,
    firestore: FirestoreRef = firestore(SystemEnv.cloudProject, production),
) : PubSubSubscriber<Hourly>() {
    private val stripeKeysRepository = TypedCollectionReference<StripeKeys>(firestore.firestore["constants"])
    private val stripeClients = StripeClients(SystemEnv.stripeKeyEu, SystemEnv.stripeKeyUs)
    private val defaultReverseParams = TransferReversalCollectionCreateParams.builder().build()

    override fun consume(payload: Hourly) {
        // note this will refund also USD as these are currently in the same Stripe account
        stripeClients[Currency.EUR]
            .reverseRefunds()
            .reverseLostChargebacks()

        return
        // TODO allow once US clients migrated
        stripeClients[Currency.USD]
            .reverseRefunds()
            .reverseLostChargebacks()
    }

    private fun StripeClient.reverseRefunds(since: Instant = Instant.now().minusHours(24)): StripeClient {
        val refundListParams = RefundListParams.builder()
            .setCreated(RefundListParams.Created.builder().setGte(since.epochSecond).build())
            .addExpand("data.charge.transfer")
            .addExpand("data.charge.transfer.destination_payment")
            .build()

        val refunds = this.refunds()
            .list(refundListParams)
            .autoPagingIterable()

        for (refund in refunds) {
            reverseTransferAndAppFees(refund.chargeObject.transferObject)
        }
        return this
    }

    private fun StripeClient.reverseLostChargebacks(since: Instant = Instant.now().minusDays(90)) {
        val disputeListParams = DisputeListParams.builder()
            .setCreated(DisputeListParams.Created.builder().setGte(since.epochSecond).build())
            .addExpand("data.charge.transfer")
            .addExpand("data.charge.transfer.destination_payment")
            .build()

        val disputes = this.disputes()
            .list(disputeListParams)
            .autoPagingIterable()
            .filter { it.status == "lost" }

        for (dispute in disputes) {
            reverseTransferAndAppFees(dispute.chargeObject.transferObject)
        }
    }

    private fun reverseTransferAndAppFees(transfer: Transfer) {
        if (transfer.amountReversed < transfer.amount) {
            try {
                val reversal = transfer.reversals.create(defaultReverseParams)
                transfer.destinationPaymentObject.applicationFee
                val chargedAt = Instant.ofEpochSecond(transfer.created)
                log.info(
                    "Reversed refunded transfer ${transfer.id} " +
                        "originally charged at $chargedAt: ${reversal.id}",
                )
            } catch (e: Exception) {
                log.fatal("Cannot reverse transfer ${transfer.id}: ${e.message}")
            }
            try {
                val result = stripeClients[transfer.currency].applicationFees().refunds()
                    .create(transfer.destinationPaymentObject.applicationFee)
                log.info(
                    "Reversed application fee ${transfer.destinationPaymentObject.applicationFee}" +
                        " for ${transfer.id}/${transfer.destinationPaymentObject.id}: ${result.id}",
                )
            } catch (e: Exception) {
                log.error(
                    "Cannot refund application fee for " +
                        "${transfer.id}/${transfer.destinationPaymentObject.applicationFee}: ${e.message}",
                )
            }
        }
    }
}
