package hero.functions

import com.stripe.model.Account
import com.stripe.model.Balance
import com.stripe.net.RequestOptions
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.baseutils.minusDays
import hero.gcloud.FirestoreRef
import hero.gcloud.firestore
import hero.gcloud.isFalse
import hero.gcloud.isNotNull
import hero.gcloud.root
import hero.gcloud.typedCollectionOf
import hero.gcloud.union
import hero.gcloud.where
import hero.model.Creator
import hero.model.User
import hero.model.topics.Daily
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.runBlocking
import java.time.Instant
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit

@Suppress("unused")
class StripeInactiveAccountsRemover(
    private val production: Boolean = SystemEnv.isProduction,
    private val firestore: FirestoreRef = firestore(SystemEnv.cloudProject, production),
) : PubSubSubscriber<Daily>() {
    private val usersCollection = firestore.typedCollectionOf(User)

    override fun consume(payload: Daily) {
        val creators = usersCollection
            .where(root(User::creator).path(Creator::stripeAccountId)).isNotNull()
            .and(root(User::creator).path(Creator::stripeAccountOnboarded)).isFalse()
            .and(root(User::creator).path(Creator::stripeAccountActive)).isFalse()
            .fetchAll()

        log.info("Found ${creators.size} creators with inactive Stripe account")

        val pool = Executors.newFixedThreadPool(POOL_SIZE)
        runBlocking(context = pool.asCoroutineDispatcher()) {
            val deletedCount = creators
                .map { creator ->
                    async {
                        handleInactiveCreator(creator)
                    }
                }
                .awaitAll()
                .count { it }

            log.info("Deleted $deletedCount/${creators.size} Stripe accounts")
        }
        pool.apply {
            shutdown()
            awaitTermination(AWAIT_TIMEOUT, TimeUnit.MINUTES)
        }
    }

    /**
     * @return *true* if creator was successfully deleted otherwise *false*
     */
    @Suppress("ReturnCount")
    private fun handleInactiveCreator(creator: User): Boolean {
        val accountId = creator.creator.stripeAccountId!!
        if ((creator.counts.incomes ?: 0) > 0L || creator.counts.supporters > 0L) {
            log.error("Not onboarded user has non-zero supporters: ${creator.id}/$accountId")
            return false
        }

        val account: Account = try {
            Account.retrieve(accountId)
        } catch (e: Exception) {
            log.error("Could not retrieve account $accountId", mapOf("accountId" to accountId), e)
            return false
        }

        val createdAt = Instant.ofEpochSecond(account.created)
        if (createdAt > Instant.now().minusDays(CREATED_AT_THRESHOLD)) {
            log.debug("Account $accountId is fresh (created at $createdAt), not yet deleting.")
            return false
        }

        val balance = Balance.retrieve(RequestOptions.builder().setStripeAccount(accountId).build())
        val balanceAvailable =
            (balance.available.firstOrNull()?.amount ?: 0L) + (balance.pending.firstOrNull()?.amount ?: 0L)
        if (balanceAvailable != 0L) {
            log.error("User ${creator.id}/$accountId has non-zero balance despite it was not onboarded.")
            return false
        }

        log.info("Deleting creator ${creator.id} stripe account $accountId", mapOf("userId" to creator.id))
        try {
            account.delete()
        } catch (e: Exception) {
            log.error("Cannot delete account ${creator.id}/$accountId", cause = e)
        } finally {
            val creatorId = creator.id
            usersCollection[creatorId].field(root(User::creator).path(Creator::stripeAccountId)).update(null)
            usersCollection[creatorId].field(root(User::creator).path(Creator::stripeAccountLegacyIds)).union(accountId)
        }

        return true
    }
}

private const val CREATED_AT_THRESHOLD = 20L
private const val AWAIT_TIMEOUT = 60L
private const val POOL_SIZE = 8
