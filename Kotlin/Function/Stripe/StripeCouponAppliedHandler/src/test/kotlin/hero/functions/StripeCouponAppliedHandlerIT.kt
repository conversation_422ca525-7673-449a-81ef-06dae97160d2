package hero.functions

import com.stripe.model.PaymentMethod
import com.stripe.param.CustomerCreateParams
import com.stripe.param.PaymentIntentCreateParams
import com.stripe.param.PaymentMethodCreateParams
import com.stripe.param.PriceCreateParams.Recurring.Interval.MONTH
import com.stripe.param.TransferListParams
import hero.baseutils.SystemEnv
import hero.baseutils.systemEnv
import hero.gcloud.PubSub
import hero.model.Currency.EUR
import hero.model.Tier
import hero.model.topics.CardCreateType
import hero.model.topics.CouponApplied
import hero.stripe.service.StripeClients
import hero.stripe.service.StripeCouponService
import hero.stripe.service.StripePaymentMethodsService
import hero.stripe.service.StripeService
import hero.stripe.service.StripeSubscriptionService
import hero.stripe.service.VatMappingProvider
import hero.test.IntegrationTestHelper
import hero.test.StripeHelper
import hero.test.euStripeConnectedAccount
import io.mockk.clearAllMocks
import io.mockk.mockk
import io.mockk.unmockkAll
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.assertNotNull
import java.math.BigDecimal
import java.time.Instant
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertFalse

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@Disabled("https://linear.app/herohero/issue/HH-1033/define-general-herohero-vouchers")
internal class StripeCouponAppliedHandlerIT {
    private val isProduction = false
    private val testHelper = IntegrationTestHelper()
    private val pubSub: PubSub = mockk(relaxed = true)
    private val stripeClients: StripeClients = StripeClients(SystemEnv.stripeKeyEu, SystemEnv.stripeKeyUs)
    private val stripeHelper = StripeHelper(stripeClients[EUR])

    private val stripeService = StripeService(
        clients = stripeClients,
        pubSub = pubSub,
    )

    private val stripePaymentMethodsService = StripePaymentMethodsService(
        clients = stripeClients,
        service = stripeService,
        pubSub = pubSub,
    )

    private val stripeCouponService = StripeCouponService(stripeClients)

    private val subscriptionService = StripeSubscriptionService(
        clients = stripeClients,
        paymentMethodsService = stripePaymentMethodsService,
        production = isProduction,
        countryToVatMapping = VatMappingProvider(systemEnv("FLEXIBEE_PASSWORD")).countryToVatMapping(),
        recurringPeriod = MONTH,
        stripeCouponService = stripeCouponService,
    )

    private val underTest = StripeCouponAppliedHandler(
        stripeClients = stripeClients,
        stripeService = stripeService,
    )

    @AfterEach
    fun afterEach() {
        clearAllMocks()
        unmockkAll()
    }

    @AfterAll
    fun afterAll() {
        stripeHelper.testCleanUp()
    }

    @Test
    fun `pay out creator from the herohero general coupon value`() {
        val testTier = Tier.ofId("EUR05")
        val creator = testHelper.createUser(
            tierId = testTier.id,
            currency = testTier.currency,
            stripeAccountId = euStripeConnectedAccount,
        )
        val coupon = stripeCouponService.createCoupon(
            purchasedByUserId = "user-purchasing-herohero-coupon-id",
            creatorId = creator.id,
            couponCode = UUID.randomUUID().toString(),
            tier = testTier,
            price = null,
            months = 3,
            days = null,
            percentOff = null,
            campaign = this::class.simpleName!!,
            currency = EUR,
        )
        val couponAmountCents = coupon.durationInMonths * testTier.priceCents
        val customer = stripeClients[EUR].customers().create(
            CustomerCreateParams.builder()
                .setEmail(creator.email)
                .build(),
        )
        val paymentMethod = paymentMethod(customer.id, true)
        stripeClients[EUR].paymentIntents().create(
            PaymentIntentCreateParams.builder()
                .setPaymentMethod(paymentMethod.id)
                .setConfirm(true)
                .setOffSession(true)
                .setAmount(couponAmountCents)
                .setCurrency(testTier.currency.name)
                .setCustomer(customer.id)
                .setAutomaticPaymentMethods(
                    PaymentIntentCreateParams.AutomaticPaymentMethods.builder()
                        .setEnabled(true)
                        .setAllowRedirects(PaymentIntentCreateParams.AutomaticPaymentMethods.AllowRedirects.NEVER)
                        .build(),
                )
                .setTransferData(
                    PaymentIntentCreateParams.TransferData.builder()
                        .setDestination("heroheroCouponAccountId")
                        .setAmount(couponAmountCents)
                        .build(),
                )
                .setTransferGroup(coupon.id)
                .build(),
        )
        val price = subscriptionService.createPrice(creator, testTier)
        val subscription = subscriptionService.createSubscription(
            customerId = customer.id,
            paymentMethodId = null,
            couponId = coupon.id,
            tier = testTier,
            priceId = price.id,
            creatorId = creator.id,
            userId = "test-user-id",
            creatorStripeAccountId = euStripeConnectedAccount,
            creatorCountry = "CZ",
            subscribed = Instant.now(),
            isResubscription = false,
            creatorVatId = "CZ123456789",
            cardCreateType = null,
            currency = testTier.currency,
        )

        // after subscription creation there is still only single transfer - to our coupon account
        val transfersBeforeConsumer = stripeClients[EUR].transfers().list(
            TransferListParams.builder().setTransferGroup(coupon.id).build(),
        ).data
        assertEquals(1, transfersBeforeConsumer.size)
        assertFalse(transfersBeforeConsumer.first().reversed)
        assertEquals("heroheroCouponAccountId", transfersBeforeConsumer.first().destination)

        // now we run the consumer
        underTest.consume(CouponApplied(subscription.id, EUR))

        // there should now be creator transfer created
        val transfers = stripeClients[EUR].transfers()
            .list(TransferListParams.builder().setTransferGroup(coupon.id).build())
            .data
        val couponTransfer = transfers.firstOrNull { it.destination == "heroheroCouponAccountId" }
        val creatorTransfer = transfers.firstOrNull { it.destination == euStripeConnectedAccount }

        // coupon transfer must have the same amount as the coupon and must be reversed
        assertNotNull(couponTransfer)
        assertTrue(couponTransfer.reversed)
        assertEquals(couponAmountCents, couponTransfer.amount)

        // creator transfer must have 90%-VAT value of the coupon (fee) and must not be reversed
        val expectedTransferCents = BigDecimal("87.9")
            .times(couponTransfer.amount.toBigDecimal())
            .divide(BigDecimal(100))
            .toLong()
        assertNotNull(creatorTransfer)
        assertEquals(expectedTransferCents, creatorTransfer.amount)
        assertFalse(creatorTransfer.reversed)

        // running the consumer again will be no-operation and transfers stay the same
        underTest.consume(CouponApplied(subscription.id, EUR))
        val transfersSecondTime = stripeClients[EUR].transfers().list(
            TransferListParams.builder().setTransferGroup(coupon.id).build(),
        ).data
        assertEquals(transfers, transfersSecondTime)
    }

    @Test
    fun `creators coupons do not invoke any transfers`() {
        val testTier = Tier.ofId("EUR05")
        val client = stripeClients[testTier.currency]
        val creator = testHelper.createUser(
            tierId = testTier.id,
            currency = testTier.currency,
            stripeAccountId = euStripeConnectedAccount,
        )
        val price = subscriptionService.createPrice(creator, testTier)
        val coupon = stripeCouponService.createCoupon(
            purchasedByUserId = "user-purchasing-creators-coupon-id",
            creatorId = creator.id,
            couponCode = UUID.randomUUID().toString(),
            tier = testTier,
            price = price,
            months = 3,
            days = null,
            percentOff = null,
            campaign = this::class.simpleName!!,
            currency = testTier.currency,
        )
        val couponAmountCents = coupon.durationInMonths * testTier.priceCents
        val customer = client.customers().create(
            CustomerCreateParams.builder()
                .setEmail(creator.email)
                .build(),
        )
        val paymentMethod = paymentMethod(customer.id, true)
        client.paymentIntents().create(
            PaymentIntentCreateParams.builder()
                .setPaymentMethod(paymentMethod.id)
                .setConfirm(true)
                .setOffSession(true)
                .setAmount(couponAmountCents)
                .setCurrency(testTier.currency.name)
                .setCustomer(customer.id)
                .setAutomaticPaymentMethods(
                    PaymentIntentCreateParams.AutomaticPaymentMethods.builder()
                        .setEnabled(true)
                        .setAllowRedirects(PaymentIntentCreateParams.AutomaticPaymentMethods.AllowRedirects.NEVER)
                        .build(),
                )
                .setTransferData(
                    PaymentIntentCreateParams.TransferData.builder()
                        .setDestination(creator.creator.stripeAccountId)
                        .setAmount(couponAmountCents)
                        .build(),
                )
                .setTransferGroup(coupon.id)
                .build(),
        )
        val subscription = subscriptionService.createSubscription(
            customerId = customer.id,
            paymentMethodId = null,
            couponId = coupon.id,
            tier = testTier,
            priceId = price.id,
            creatorId = creator.id,
            userId = "test-user-id",
            creatorStripeAccountId = euStripeConnectedAccount,
            creatorCountry = "CZ",
            subscribed = Instant.now(),
            isResubscription = false,
            creatorVatId = "CZ123456789",
            cardCreateType = null,
            currency = testTier.currency,
        )

        // we need to wait until transfers propagate to be listed below
        Thread.sleep(1000)

        // after subscription creation there is still only single transfer - to our coupon account
        val transfersBeforeConsumer = client.transfers()
            .list(TransferListParams.builder().setTransferGroup(coupon.id).build())
            .data
        assertEquals(1, transfersBeforeConsumer.size)
        assertFalse(transfersBeforeConsumer.first().reversed)
        assertEquals(creator.creator.stripeAccountId, transfersBeforeConsumer.first().destination)

        // now we run the consumer
        underTest.consume(CouponApplied(subscription.id, EUR))

        // there should now be creator transfer created
        val transfersAfterConsumer = stripeClients[EUR].transfers()
            .list(TransferListParams.builder().setTransferGroup(coupon.id).build())
            .data
        // creator's account will have no change
        assertEquals(1, transfersAfterConsumer.size)
        assertFalse(transfersAfterConsumer.first().reversed)
        assertEquals(creator.creator.stripeAccountId, transfersAfterConsumer.first().destination)
    }

    // TODO shift to integration helpers – not trivial because of relation to StripeService
    private fun paymentMethod(
        customerId: String,
        valid: Boolean,
    ): PaymentMethod {
        val paymentMethod = stripeClients[EUR].paymentMethods().create(
            PaymentMethodCreateParams
                .builder()
                .setType(PaymentMethodCreateParams.Type.CARD)
                .setCard(
                    PaymentMethodCreateParams.Token.builder()
                        .setToken(if (valid) "tok_visa" else "tok_chargeDeclined")
                        .build(),
                )
                .build(),
        )
        // we associate the new invalid payment method with the customer
        stripePaymentMethodsService.putPaymentMethodViaSetupIntent(
            paymentMethodId = paymentMethod.id,
            customerId = customerId,
            cardCreateType = CardCreateType.CARD,
            confirm = true,
            makeDefault = true,
            currency = EUR,
        )

        return paymentMethod
    }
}
