package hero.functions

import com.stripe.model.Charge
import hero.baseutils.log
import hero.gcloud.PubSub
import hero.model.Currency
import hero.model.topics.BigQueryWriteRequest
import hero.stripe.service.StripeClients
import java.time.Instant

class StripeChargeStatisticsHandler(
    private val pubSub: PubSub,
    private val stripeClients: StripeClients,
) {
    fun handle(charge: Charge) {
        if (charge.status !in listOf("succeeded", "failed")) {
            return
        }
        if (charge.customer == null) {
            log.info("Charge ${charge.id} is missing customer id, skipping writing to <PERSON>Query")
            return
        }

        val paymentMethod = stripeClients[charge.currency].paymentMethods().retrieve(charge.paymentMethod)

        pubSub.publish(
            BigQueryWriteRequest(
                id = charge.id,
                table = "stripe-charges",
                model = mapOf(
                    // https://dashboard.stripe.com/{test}/payments/{charge.id}
                    "chargeId" to charge.id,
                    "status" to charge.status,
                    "amount" to charge.amount,
                    "customerId" to charge.customer,
                    "paymentIntentId" to charge.paymentIntent,
                    "paymentMethodId" to charge.paymentMethod,
                    "paymentMethodType" to paymentMethod.metadata["cardCreateType"],
                    "cardBrand" to charge.paymentMethodDetails.card?.brand,
                    "cardCountry" to charge.paymentMethodDetails.card?.country,
                    "card3DS" to (charge.paymentMethodDetails.card?.threeDSecure?.result),
                    "cardCvcCheck" to charge.paymentMethodDetails.card?.checks?.cvcCheck,
                    "succeededCharges" to 1,
                    "status" to charge.status,
                    "failureCode" to charge.failureCode,
                    "failureMessage" to charge.failureMessage,
                    "timestamp" to Instant.ofEpochSecond(charge.created).toString(),
                    "currency" to charge.currency.uppercase(),
                ),
                updatable = false,
            ),
        )
    }

    fun handle(
        chargeId: String?,
        paymentMethodId: String,
        customerId: String,
        succeededCharges: Int,
        declineCode: String?,
        declineMessage: String?,
        currency: Currency,
    ) {
        val paymentMethod = stripeClients[currency].paymentMethods().retrieve(paymentMethodId)
        pubSub.publish(
            BigQueryWriteRequest(
                id = chargeId,
                table = "stripe-charges",
                model = mapOf(
                    "chargeId" to chargeId,
                    "amount" to null,
                    "customerId" to customerId,
                    "paymentMethodId" to paymentMethodId,
                    "paymentMethodType" to paymentMethod.metadata["cardCreateType"],
                    "succeededCharges" to succeededCharges,
                    "paymentIntentId" to null,
                    "status" to "failed",
                    "failureCode" to declineCode,
                    "failureMessage" to declineMessage,
                    "timestamp" to Instant.now().toString(),
                    "currency" to null,
                ),
                updatable = false,
            ),
        )
    }
}
