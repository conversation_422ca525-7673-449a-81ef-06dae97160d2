package hero.functions

import com.stripe.param.SubscriptionSearchParams
import com.stripe.param.SubscriptionUpdateParams
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.baseutils.systemEnv
import hero.exceptions.http.ServerException
import hero.model.Currency
import hero.model.Tier
import hero.model.topics.FeesUpdateRequested
import hero.stripe.service.StripeClients
import hero.stripe.service.VatMappingProvider
import hero.stripe.service.computeFee
import hero.stripe.service.stripeRetry
import java.time.Instant
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit

@Suppress("unused")
class StripeFeesUpdater(production: Boolean = SystemEnv.isProduction) : PubSubSubscriber<FeesUpdateRequested>() {
    private val stripeClients: StripeClients =
        StripeClients(SystemEnv.stripeKeyEu, SystemEnv.stripeKeyUs, production)
    private val countryToVatMapping = VatMappingProvider(systemEnv("FLEXIBEE_PASSWORD")).countryToVatMapping()

    override fun consume(payload: FeesUpdateRequested) {
        val tier = Tier.ofId(payload.tierId)
        val (transferPercents, feePercents, feeVatPercents) = computeFee(
            feePercents = tier.feePercents,
            creatorVatId = payload.vatId,
            creatorCountry = payload.country,
            instant = Instant.now(),
            countryToVatMapping = countryToVatMapping,
        )
        val applicationFee = if (tier.currency != Currency.USD)
            null
        else
            feePercents

        if (transferPercents == null && applicationFee == null) {
            throw ServerException(
                "Inconsistent fee (both transfer and application fee was null) required for $payload.",
                mapOf("creatorId" to payload.creatorId),
            )
        }

        val executor = Executors.newFixedThreadPool(20)
        val list = stripeClients[tier.currency]
            .subscriptions()
            .search(
                SubscriptionSearchParams.builder()
                    .setQuery(
                        // we find creator's subscriptions which don't match expected fees
                        "metadata['creatorId']:'${payload.creatorId}'" +
                            " AND status:'active'" +
                            if (!payload.forced)
                                " AND -metadata['transferPercents']:'$transferPercents'" +
                                    " AND -metadata['appFeeTotal']:'${tier.feeAbsolute}'"
                            else
                                "",
                    ).build(),
            )
            .autoPagingIterable()
            .toList()

        list
            .forEach {
                executor.submit {
                    val updateParamsTransfer = SubscriptionUpdateParams.builder()
                        .setTransferData(
                            SubscriptionUpdateParams.TransferData.builder()
                                .setAmountPercent(transferPercents)
                                .setDestination(it.transferData.destination)
                                .build(),
                        )
                        .setMetadata(
                            it.metadata.minus("fee").minus("feeVat") + mapOf(
                                "transferPercents" to transferPercents.toString(),
                                "feePercents" to feePercents.toString(),
                                "feeVatPercents" to feeVatPercents.toString(),
                                "transferPercents" to transferPercents.toString(),
                                "creatorCountry" to payload.country,
                                "creatorVatId" to payload.vatId,
                                "appFeeHerohero" to tier.heroheroFeeAbsolute.toString(),
                                "appFeeStripeDynamic" to tier.dynamicStripeFeeAbsolute.toString(),
                                "appFeeStripeFixed" to tier.stripeFeeFixed.toString(),
                                "appFeeTotal" to tier.feeAbsolute.toString(),
                            ),
                        )
                        .build()

                    // application fee cannot be set at the same time as non-100% transfer
                    val updateParamsAppFee = SubscriptionUpdateParams.builder()
                        .setApplicationFeePercent(applicationFee)
                        .build()

                    val meta = it.metadata
                    val logPayload = mapOf(
                        "creatorId" to payload.creatorId,
                        "userId" to meta["userId"],
                        "subscriptionId" to it.id,
                        "transferAmountOriginal" to meta["transferPercents"],
                        "transferAmountNew" to transferPercents,
                        "appFeeTotal" to tier.feeAbsolute,
                    )

                    log.info(
                        "Updating ${it.id} of ${payload.creatorId}: " +
                            "${meta["transferPercents"] ?: meta["transferCents"]} -> $transferPercents, " +
                            "${meta["appFeeTotal"]} -> ${tier.feeAbsolute}",
                    )
                    try {
                        stripeRetry {
                            // these possibly cannot be set together
                            it.update(updateParamsTransfer)
                            it.update(updateParamsAppFee)
                        }
                    } catch (e: Exception) {
                        log.fatal("Cannot overwrite subscription fees: ${e.message}", logPayload, e)
                    }
                }
            }

        executor.shutdown()
        executor.awaitTermination(Long.MAX_VALUE, TimeUnit.HOURS)
    }
}

fun main() {
    StripeFeesUpdater(true)
        .consume(
            FeesUpdateRequested(
                creatorId = "aaa",
                vatId = null,
                tierId = "EUR00",
                country = "EU",
                forced = false,
            ),
        )
}
