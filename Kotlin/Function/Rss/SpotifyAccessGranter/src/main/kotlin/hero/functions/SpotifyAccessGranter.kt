package hero.functions

import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.baseutils.retryOn
import hero.exceptions.http.HttpStatusException
import hero.exceptions.http.NotFoundException
import hero.gcloud.FirestoreRef
import hero.gcloud.TypedCollectionReference
import hero.gcloud.fetchSingle
import hero.gcloud.firestore
import hero.gcloud.get
import hero.gcloud.typedCollectionOf
import hero.model.SpotifyCredentials
import hero.model.User
import hero.model.topics.SubscriberStatusChange.SUBSCRIBED
import hero.model.topics.SubscriberStatusChange.UNSUBSCRIBED
import hero.model.topics.SubscriberStatusChanged
import hero.spotify.SpotifyOpenClient

@Suppress("Unused")
class SpotifyAccessGranter(
    private val production: Boolean = SystemEnv.isProduction,
    private val firestore: FirestoreRef = firestore(SystemEnv.cloudProject, production),
    private val usersCollection: TypedCollectionReference<User> = firestore.typedCollectionOf(User),
    private val credentials: SpotifyCredentials = firestore.firestore["constants"]["oauth-spotify"].fetchSingle()!!,
    private val client: SpotifyOpenClient =
        SpotifyOpenClient(
            credentials.appId,
            credentials.appSecret,
            credentials.partnerId,
        ),
) : PubSubSubscriber<SubscriberStatusChanged>() {
    override fun consume(payload: SubscriberStatusChanged) {
        val user = usersCollection[payload.userId].fetch()
        if (user?.spotify?.id == null) {
            return
        }
        val creator = usersCollection[payload.creatorId].fetch()
            ?: return

        val creatorMessage = "${payload.creatorId}/${creator.spotifyUri ?: "rss-feed-not-exported-yet"}"
        val labels = mapOf("userId" to user.id, "creatorId" to creator.id)

        try {
            // make sure user has not unlinked Spotify in the UI at https://content-access.spotify.com/
            client.getEntlitlements(payload.userId)
        } catch (e: NotFoundException) {
            log.info("User ${payload.userId} has removed their Spotify link, clearing credentials.", labels)
            usersCollection[payload.userId].field(User::spotify).update(null)
            return
        }

        when (payload.statusChange) {
            SUBSCRIBED -> {
                log.info("Granting ${payload.userId} to access Spotify content of $creatorMessage", labels)
                retryOn(HttpStatusException::class) {
                    client.addEntitlements(payload.userId, listOf(payload.creatorId))
                }
            }

            UNSUBSCRIBED -> {
                log.info("Revoking ${payload.userId} to access Spotify content of $creatorMessage", labels)
                retryOn(HttpStatusException::class) {
                    client.deleteEntitlements(payload.userId, listOf(payload.creatorId))
                }
            }
        }

        log.info("User ${payload.userId} now has entitlements: ${client.getEntlitlements(payload.userId)}")
    }
}
