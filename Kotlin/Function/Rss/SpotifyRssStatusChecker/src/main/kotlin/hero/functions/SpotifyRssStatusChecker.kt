package hero.functions

import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.gcloud.FirestoreRef
import hero.gcloud.TypedCollectionReference
import hero.gcloud.fetchSingle
import hero.gcloud.firestore
import hero.gcloud.get
import hero.gcloud.isFalse
import hero.gcloud.isNotNull
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.SpotifyCredentials
import hero.model.User
import hero.model.topics.Minutely
import hero.spotify.SpotifyRssClient
import java.time.LocalDateTime

@Suppress("Unused")
class SpotifyRssStatusChecker(
    private val production: Boolean = SystemEnv.isProduction,
    private val firestore: FirestoreRef = firestore(SystemEnv.cloudProject, production),
    private val usersCollection: TypedCollectionReference<User> = firestore.typedCollectionOf(User),
    private val credentials: SpotifyCredentials = firestore.firestore["constants"]["oauth-spotify"].fetchSingle()!!,
    private val client: SpotifyRssClient = SpotifyRssClient(credentials.rssFeedToken),
) : PubSubSubscriber<Minutely>() {
    override fun consume(payload: Minutely) {
        if (LocalDateTime.now().minute % 10 != 0) {
            // let's check only once in 10 mins, Spotify refreshes feeds once in 30 minutes or even longer
            return
        }

        val usersToCheck = usersCollection.where(User::spotifyUri).isNotNull()
            .and(User::spotifyFeedReady).isFalse()
            .fetchAll()

        usersToCheck.forEach {
            val status = client.getRssFeed(it.spotifyUri!!)
            when (status.statusCode) {
                1 -> {
                    // awaiting ingestion, no-op
                    log.info("Feed ${it.spotifyUri} of ${it.id} has not yet been ingested.")
                }
                2 -> {
                    val errors = status.validationErrors ?: emptyList()
                    if (errors.size <= 2 && errors.any { "empty fields: episode" in it.message }) {
                        // the feed is just empty, no need for alert
                        log.info("Marking ${it.spotifyUri} of ${it.id} as empty, but ingested.")
                        markIngested(it.id)
                        return@forEach
                    }
                    // ingested, but failed
                    log.fatal(
                        "Feed ${it.spotifyUri} of ${it.id} failed ingestion: ${status.statusDescription}, " +
                            errors.map { it.message },
                    )
                }
                3 -> {
                    // correctly ingested
                    log.info("Marking ${it.spotifyUri} of ${it.id} as correctly ingested.")
                    markIngested(it.id)
                }
                4 -> {
                    // too many requests, skipping
                }
            }
        }
    }

    private fun markIngested(userId: String) {
        usersCollection[userId].field(User::spotifyFeedReady).update(true)
    }
}

fun main() {
    SpotifyRssStatusChecker(true).consume(Minutely())
}
