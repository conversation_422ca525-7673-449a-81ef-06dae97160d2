package hero.functions

import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.core.logging.Logger
import hero.gcloud.FirestoreRef
import hero.gcloud.fetchSingle
import hero.gcloud.firestore
import hero.gcloud.get
import hero.jackson.map
import hero.model.SpotifyCredentials
import hero.model.topics.PostStateChanged
import hero.repository.post.PostRepository
import hero.repository.user.UserRepository
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import org.jooq.DSLContext

@Suppress("Unused")
class RssFeedUpdater(
    lazyContext: Lazy<DSLContext> = lazy { JooqSQL.context(ConnectorConnectionPool.dataSource) },
    private val userRepository: UserRepository = UserRepository(lazyContext),
    private val postRepository: PostRepository = PostRepository(lazyContext),
    private val logger: Logger = log,
    private val envPrefix: String = "svc-" + SystemEnv.environment.replace("local", "devel"),
    private val firestore: FirestoreRef = firestore(SystemEnv.cloudProject, SystemEnv.isProduction),
    private val credentials: SpotifyCredentials = firestore.firestore["constants"]["oauth-spotify"]
        .fetchSingle<SpotifyCredentials>()!!,
) : PubSubSubscriber<PostStateChanged>() {
    private val context by lazyContext

    override fun consume(payload: PostStateChanged) {
        val postId = payload.post.id
        if (payload.post.parentId != null) {
            logger.info("Ignoring non content post $postId")
            return
        }

        val userId = payload.post.userId
        val creator = userRepository.getById(userId)
        if (!creator.hasRssFeed && creator.spotifyUri == null) {
            logger.info("Ignoring post $postId since creator ${creator.id} does not have rss nor spotify enabled")
            return
        }

        val post = postRepository.getById(postId)
        logger.info(
            "Updating RSS feed for creator ${creator.id} and post $postId",
            mapOf("postId" to postId, "userId" to userId) + post.map(),
        )

        val command = GenerateAndStoreRssFeed(creator = creator, envPrefix = envPrefix, credentials = credentials)
        RssFeedGenerator(postRepository = postRepository).execute(command)

        logger.info(
            "Finished updating RSS feed for creator ${creator.id} and post $postId",
            mapOf("postId" to postId, "userId" to userId),
        )
    }
}
