package hero.functions

import com.github.kittinunf.fuel.httpPost
import hero.baseutils.SystemEnv
import hero.baseutils.fetch
import hero.baseutils.log
import hero.baseutils.serviceCall
import hero.gcloud.FirestoreRef
import hero.gcloud.TypedCollectionReference
import hero.gcloud.fetchSingle
import hero.gcloud.firestore
import hero.gcloud.get
import hero.gcloud.typedCollectionOf
import hero.jwt.ACCESS_TOKEN
import hero.jwt.authorization
import hero.model.FeedUrlResponse
import hero.model.SpotifyCredentials
import hero.model.User
import hero.spotify.SpotifyRssClient

@Suppress("Unused")
class SpotifyRssPublisher(
    private val production: Boolean = SystemEnv.isProduction,
    private val firestore: FirestoreRef = firestore(SystemEnv.cloudProject, production),
    private val usersCollection: TypedCollectionReference<User> = firestore.typedCollectionOf(User),
    private val credentials: SpotifyCredentials = firestore.firestore["constants"]["oauth-spotify"].fetchSingle()!!,
    private val client: SpotifyRssClient = SpotifyRssClient(credentials.rssFeedToken),
) : FirestoreEventSubcriber() {
    override fun consume(event: FirestoreEvent) {
        exportUser(event.documentId)
    }

    private fun exportUser(userId: String) {
        val user = usersCollection[userId].fetch()
            ?: return

        if (!user.hasSpotifyExport && user.spotifyUri != null) {
            client.deleteRssFeed(user.spotifyUri!!)
            usersCollection[userId].field(User::spotifyUri).update(null)
            log.info("Spotify feed for $userId/${user.spotifyUri} was removed.", mapOf("userId" to userId))
        }

        // add or remove Spotify feed of myself as a creator
        if (user.hasSpotifyExport && user.spotifyUri == null) {
            val feedUrl = serviceCall("api", "/v1/users/${user.id}/rss-feed?isSpotify=true")
                .httpPost()
                .header("Cookie", "$ACCESS_TOKEN=${user.id.authorization()}")
                .fetch<FeedUrlResponse>()
                .url
            try {
                val spotifyFeed = client.postRssFeed(feedUrl)
                usersCollection[userId].field(User::spotifyUri).update(spotifyFeed.spotifyUri)
                usersCollection[userId].field(User::spotifyFeedReady).update(false)
                log.info("Spotify feed for $userId was stored as ${spotifyFeed.spotifyUri}", mapOf("userId" to userId))
            } catch (e: Exception) {
                log.fatal("Could not publish feed for $userId: ${e.message}/$feedUrl")
            }
        }
    }
}

// TODO remove after debugging finished
// one-off tool for deleting existing Stripe RSS showss
fun main() {
    val production = false
    val firestore: FirestoreRef = firestore(SystemEnv.cloudProject, production)
    val credentials: SpotifyCredentials = firestore.firestore["constants"]["oauth-spotify"].fetchSingle()!!
    val client = SpotifyRssClient(credentials.rssFeedToken)

    toDelete.forEach { println(it + " " + client.deleteRssFeed(it)) }
}

val toDelete = """
spotify:show:68SRwP903qUwwppa0wblBP
spotify:show:1BZCUlwowvaPcvTGpPU23M
""".trimIndent()
    .split("\\s+".toRegex()).map { it.trim() }
