Kotlin/Function/Post/PostNotifier/build:
  stage: build-services
  needs:
    - Kotlin/Function/Subscriber/build
    - Kotlin/Modules/BaseUtils/build
    - Kotlin/Modules/Core/build
    - Kotlin/Modules/GoogleCloud/build
    - Kotlin/Modules/Jwt/build
    - Kotlin/Modules/Model/build
    - Kotlin/Modules/SQL/build
  extends:
    - .Kotlin/job-build-gradle-module
    - .Function/variables-build

.Kotlin/Function/Post/PostNotifier/variables:
  variables:
    TIMEOUT: "540s"
    # required to have 2 CPUs so we can send all emails in 540s timeout
    MEMORY: 4096Mi
    FUNCTION_NAME: "post-notifier"
    CLASS_NAME: "hero.functions.PostNotifier"
    TOPIC: "Minutely"
    # TODO limit service account to SQL + Firestore

Kotlin/Function/Post/PostNotifier/deploy-devel:
  needs:
    - Kotlin/Function/Post/PostNotifier/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-devel
    - .Kotlin/Function/Post/PostNotifier/variables

Kotlin/Function/Post/PostNotifier/deploy-prod:
  needs:
    - Kotlin/Function/Post/PostNotifier/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-prod
    - .Kotlin/Function/Post/PostNotifier/variables
