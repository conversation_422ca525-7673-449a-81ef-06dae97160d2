Kotlin/Function/Post/PostSubscriberGroupNotifier/build:
  stage: build-services
  needs:
    - Kotlin/Function/Subscriber/build
    - Kotlin/Modules/BaseUtils/build
    - Kotlin/Modules/GoogleCloud/build
    - Kotlin/Modules/Jwt/build
    - Kotlin/Modules/SQL/build
    - Kotlin/Modules/Model/build
    - Kotlin/Modules/Firebase/build
    - Kotlin/Modules/Core/build
    - Kotlin/Modules/Repository/build
  extends:
    - .Kotlin/job-build-gradle-module
    - .Function/variables-build

.Kotlin/Function/Post/PostSubscriberGroupNotifier/variables:
  variables:
    TIMEOUT: "540s"
    # required to have 2 CPUs so we can send all emails in 540s timeout
    MEMORY: 4096Mi
    FUNCTION_NAME: "post-subscriber-group-notifier"
    CLASS_NAME: "hero.functions.PostSubscriberGroupNotifier"
    TOPIC: "PostNotificationGroupPrepared"
    ENV_VARS: "FF_PUSH_NOTIFICATION=enabled"
    # TODO limit service account to SQL + Firestore

Kotlin/Function/Post/PostSubscriberGroupNotifier/deploy-devel:
  needs:
    - Kotlin/Function/Post/PostSubscriberGroupNotifier/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-devel
    - .Kotlin/Function/Post/PostSubscriberGroupNotifier/variables

Kotlin/Function/Post/PostSubscriberGroupNotifier/deploy-prod:
  needs:
    - Kotlin/Function/Post/PostSubscriberGroupNotifier/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-prod
    - .Kotlin/Function/Post/PostSubscriberGroupNotifier/variables
