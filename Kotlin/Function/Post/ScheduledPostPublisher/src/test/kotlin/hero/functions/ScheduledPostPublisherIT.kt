package hero.functions

import hero.baseutils.minus
import hero.baseutils.plus
import hero.model.topics.Minutely
import hero.model.topics.PostState.DELETED
import hero.model.topics.PostState.PUBLISHED
import hero.model.topics.PostState.SCHEDULED
import hero.model.topics.PostStateChange
import hero.model.topics.PostStateChanged
import hero.sql.jooq.Tables
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper
import hero.test.TestEnvironmentVariables
import hero.test.TestRepositories
import hero.test.logging.TestLogger
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.Instant
import kotlin.time.Duration.Companion.seconds

class ScheduledPostPublisherIT : IntegrationTest() {
    @Test
    fun `should publish only post that are in state scheduled and are ready to be published`() {
        val underTest = ScheduledPostPublisher(
            lazyTestContext,
            TestRepositories.postRepository,
            firestore,
            IntegrationTestHelper.TestCollections.usersCollection,
            IntegrationTestHelper.TestCollections.postsCollection,
            pubSubMock,
            TestLogger,
            TestEnvironmentVariables,
        )

        // should be published
        val now = Instant.now()
        val post1 = testHelper.createPost("cestmir", id = "1", state = SCHEDULED, publishedAt = now - 5.seconds)

        // not ready to be published yet
        val post2 = testHelper.createPost("cestmir", id = "2", state = SCHEDULED, publishedAt = now + 5.seconds)

        // these two should be ignored since they are not in SCHEDULED state
        val post3 = testHelper.createPost("cestmir", id = "3", state = DELETED, publishedAt = now - 5.seconds)
        val post4 = testHelper.createPost("cestmir", id = "4", state = PUBLISHED, publishedAt = now - 5.seconds)

        underTest.consume(Minutely())

        val posts = TestRepositories.postRepository.find { orderBy(Tables.POST.ID) }

        assertThat(posts).containsExactly(post1.copy(state = PUBLISHED), post2, post3, post4)
        verify(exactly = 1) {
            pubSubMock.publish<PostStateChanged>(
                match {
                    it.post == post1.copy(state = PUBLISHED) && it.stateChange == PostStateChange.PUBLISHED
                },
            )
        }
    }
}
