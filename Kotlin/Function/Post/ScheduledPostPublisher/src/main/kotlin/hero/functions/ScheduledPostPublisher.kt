package hero.functions

import hero.baseutils.EnvironmentVariables
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.core.logging.Logger
import hero.gcloud.FirestoreRef
import hero.gcloud.PubSub
import hero.gcloud.TypedCollectionReference
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.model.Post
import hero.model.User
import hero.model.topics.Minutely
import hero.model.topics.PostState
import hero.model.topics.PostStateChange
import hero.model.topics.PostStateChanged
import hero.repository.post.PostRepository
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import hero.sql.jooq.Tables
import org.jooq.DSLContext
import java.time.Instant

@Suppress("Unused")
class ScheduledPostPublisher(
    lazyContext: Lazy<DSLContext> = lazy { JooqSQL.context(ConnectorConnectionPool.dataSource) },
    private val postRepository: PostRepository = PostRepository(lazyContext),
    private val firestore: FirestoreRef = firestore(SystemEnv.cloudProject, SystemEnv.isProduction),
    private val usersCollection: TypedCollectionReference<User> = firestore.typedCollectionOf(User),
    private val postsCollection: TypedCollectionReference<Post> = firestore.typedCollectionOf(Post),
    private val pubSub: PubSub = PubSub(SystemEnv.environment, SystemEnv.cloudProject),
    private val logger: Logger = log,
    systemEnv: EnvironmentVariables = SystemEnv,
) : PubSubSubscriber<Minutely>(systemEnv) {
    private val context by lazyContext

    override fun consume(payload: Minutely) {
        postRepository
            .find {
                this
                    .where(Tables.POST.STATE.eq(PostState.SCHEDULED.name))
                    .and(Tables.POST.PUBLISHED_AT.lt(Instant.now()))
            }
            .forEach {
                logger.info("Publishing post ${it.id}", mapOf("userId" to it.userId, "postId" to it.id))
                usersCollection[it.userId].field(User::lastPostAt).update(Instant.now())
                postsCollection[it.id].field(Post::state).update(PostState.PUBLISHED)
                val updatedPost = it.copy(state = PostState.PUBLISHED)
                postRepository.save(updatedPost)

                pubSub.publish(PostStateChanged(PostStateChange.PUBLISHED, updatedPost))
            }
    }
}
