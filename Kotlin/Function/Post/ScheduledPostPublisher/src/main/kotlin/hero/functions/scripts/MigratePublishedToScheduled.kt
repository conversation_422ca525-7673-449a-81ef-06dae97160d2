package hero.functions.scripts

import hero.baseutils.SystemEnv
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.Post
import hero.model.topics.PostState
import java.time.Instant

fun main() {
    val firestore = firestore(SystemEnv.cloudProject, SystemEnv.isProduction)
    val postsCollection = firestore.typedCollectionOf(Post)

    postsCollection
        .where(Post::state).isEqualTo(PostState.PUBLISHED)
        .and(Post::published).isGreaterThan(Instant.now())
        .fetchAll()
        .forEach {
            postsCollection[it.id].field(Post::state).update(PostState.SCHEDULED)
        }
}
