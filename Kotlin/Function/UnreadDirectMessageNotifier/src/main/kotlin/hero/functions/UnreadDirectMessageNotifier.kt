package hero.functions

import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.baseutils.minusHours
import hero.gcloud.FirestoreRef
import hero.gcloud.PubSub
import hero.gcloud.TypedCollectionReference
import hero.gcloud.firestore
import hero.gcloud.isFalse
import hero.gcloud.isNotNull
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.MessageThread
import hero.model.User
import hero.model.UserStatus
import hero.model.topics.EmailPublished
import hero.model.topics.Hourly
import java.time.Instant

class UnreadDirectMessageNotifier(
    private val pubSub: PubSub = PubSub(SystemEnv.environment, SystemEnv.cloudProject),
    private val firestore: FirestoreRef = firestore(SystemEnv.cloudProject, SystemEnv.isProduction),
    private val environment: String = SystemEnv.environment,
    private val domain: String = "https://" + (if (environment == "prod") "" else "$environment.") + "herohero.co",
    private val usersCollection: TypedCollectionReference<User> = firestore.typedCollectionOf(User),
    private val threadsCollection: TypedCollectionReference<MessageThread> = firestore.typedCollectionOf(MessageThread),
) : PubSubSubscriber<Hourly>() {
    override fun consume(payload: Hourly) {
        val threadsToNotify = threadsCollection
            .where(MessageThread::emailNotified).isFalse()
            .and(MessageThread::lastMessageAt).isLessThan(Instant.now().minusHours(2))
            .and(MessageThread::lastMessageAt).isNotNull()
            .fetchAll()

        val checksOfThread: Map<String, MessageThread> = threadsToNotify.flatMap { thread ->
            val checkUserId = thread.checks.filter { it.value >= thread.lastMessageAt }.keys
            val uncheckedUserIds = thread.activeFor - checkUserId
            uncheckedUserIds.map { it to thread }
        }.toMap()

        // generates map of [userIdWithUnseenMessages, [authorIdOfUnseenMessage]]
        val usersWithUnchecksFrom: Map<String, List<MessageThread>> = checksOfThread
            .entries
            .filter { it.value.lastMessageBy != null }
            .groupBy({ it.key }, { it.value })

        // generates a map of usernames [userId -> userName]
        val users: Map<String, User?> = usersWithUnchecksFrom
            .flatMap { listOf(it.key) + it.value.flatMap { messageThread -> messageThread.userIds } }
            .distinct()
            .associateWith { usersCollection[it].fetch() }
            .filter { it.value != null }

        usersWithUnchecksFrom.forEach { (userId, unchecks) ->
            val user = users[userId]
            if (user?.email == null || user.status == UserStatus.DELETED || !user.notificationsEnabled.emailNewDm) {
                return@forEach
            }
            try {
                val authorName = users[unchecks.first().lastMessageBy!!]!!.name
                pubSub.publish(
                    // https://documentation.mailgun.com/en/latest/user_manual.html#templates
                    EmailPublished(
                        from = authorName,
                        to = user.email!!,
                        // https://gitlab.com/-/ide/project/heroheroco/mailgun-templates/tree/main/-/templates/
                        template = "new-direct-message",
                        variables = listOf(
                            "messages" to mapMessagedUsers(userId, unchecks, users),
                            "user-id" to user.id,
                            "user-name" to user.name,
                            // will be shown in subject
                            "target-user-name" to authorName,
                        ),
                        language = user.language,
                    ),
                )
            } catch (e: Exception) {
                log.fatal("Cannot notify about new DM $userId: ${e.message}", mapOf("userId" to userId), e)
            }

            // and mark the thread as email-notified
            checksOfThread[userId]?.let { thread ->
                threadsCollection[thread.id].field(MessageThread::emailNotified).update(true)
            }
        }

        // some of the threads from above are possibly not yet marked as email-notified (e.g. if all users have read)
        threadsToNotify.forEach { thread ->
            threadsCollection[thread.id].field(MessageThread::emailNotified).update(true)
        }
    }

    internal fun mapMessagedUsers(
        userId: String,
        unseens: List<MessageThread>,
        users: Map<String, User?>,
    ) = unseens.mapIndexed { index, messageThread ->
        mapOf(
            "targetLink" to "$domain/messages/${messageThread.id}",
            "targetName" to users[messageThread.lastMessageBy!!]!!.name,
            "withComma" to withComma(unseens.size, index),
            "withAnd" to withAnd(unseens.size, index),
        )
    }

    internal fun withComma(
        unseensSize: Int,
        index: Int,
    ): Boolean = unseensSize > 2 && index < unseensSize - 2

    internal fun withAnd(
        unseensSize: Int,
        index: Int,
    ): Boolean = unseensSize > 1 && index == unseensSize - 2
}
