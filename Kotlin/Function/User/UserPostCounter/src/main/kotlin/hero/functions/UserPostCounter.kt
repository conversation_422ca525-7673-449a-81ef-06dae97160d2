package hero.functions

import hero.baseutils.EnvironmentVariables
import hero.baseutils.SystemEnv
import hero.baseutils.instantMin
import hero.baseutils.log
import hero.core.logging.Logger
import hero.gcloud.FirestoreRef
import hero.gcloud.TypedCollectionReference
import hero.gcloud.firestore
import hero.gcloud.root
import hero.gcloud.typedCollectionOf
import hero.model.Category
import hero.model.MessageThread
import hero.model.Post
import hero.model.SupportCounts
import hero.model.User
import hero.model.topics.PostState
import hero.model.topics.PostStateChanged
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import hero.sql.jooq.Tables.POST
import org.jooq.DSLContext
import java.time.Clock
import java.time.Instant

@Suppress("Unused")
class UserPostCounter(
    lazyContext: Lazy<DSLContext> = lazy { JooqSQL.context(ConnectorConnectionPool.dataSource) },
    private val firestore: FirestoreRef = firestore(SystemEnv.cloudProject, SystemEnv.isProduction),
    private val usersCollection: TypedCollectionReference<User> = firestore.typedCollectionOf(User),
    private val categoriesCollection: TypedCollectionReference<Category> = firestore.typedCollectionOf(Category),
    private val messageThreadsCollection: TypedCollectionReference<MessageThread> = firestore.typedCollectionOf(
        MessageThread,
    ),
    private val clock: Clock = Clock.systemUTC(),
    private val logger: Logger = log,
    envVariables: EnvironmentVariables = SystemEnv,
) : PubSubSubscriber<PostStateChanged>(envVariables) {
    private val context by lazyContext

    override fun consume(payload: PostStateChanged) {
        val post = payload.post
        if (post.parentId != null) {
            // we don't count user's comments, only posts
            logger.debug("Skipping comment (is not post) ${post.id}.", mapOf("userId" to post.userId))
            return
        }
        if (post.messageThreadId == null && post.communityId == null) {
            handleUserPostCounts(post)
        }
        if (post.messageThreadId != null) {
            handleUserMessageThreadCounts(post)
        }
    }

    private fun handleUserPostCounts(post: Post) {
        logger.info("Handling counts for Post ${post.id}.", mapOf("userId" to post.userId))
        if (post.state == PostState.PUBLISHED) {
            usersCollection[post.userId].field(User::lastPostAt).update(Instant.now(clock))
        }

        usersCollection[post.userId]
            .field(root(User::counts).path(SupportCounts::posts))
            .update(countUserPosts(post.userId))

        post.categories
            .forEach { categoryId ->
                categoriesCollection[categoryId]
                    .field(Category::postCount)
                    .update(countCategoryPosts(categoryId))
            }
    }

    // Firestore count queries are unreliable when filtering on newly added fields.
    // The count only includes documents where the field explicitly exists,
    // and ignores documents where the field is missing.
    // For example, after adding the field `communityId`, a filter `communityId == null`
    // returned only documents with `communityId` explicitly set to null,
    // excluding those without the field entirely.
    private fun countUserPosts(userId: String): Long =
        context
            .selectCount()
            .from(POST)
            .where(POST.USER_ID.eq(userId))
            .and(POST.PARENT_ID.isNull)
            .and(POST.MESSAGE_THREAD_ID.isNull)
            .and(POST.COMMUNITY_ID.isNull)
            .and(POST.STATE.eq(PostState.PUBLISHED.name))
            .fetchOne(0, Long::class.java)
            ?: 0

    private fun countCategoryPosts(categoryId: String): Long =
        context
            .selectCount()
            .from(POST)
            .where(POST.CATEGORIES.contains(arrayOf(categoryId)))
            .and(POST.PARENT_ID.isNull)
            .and(POST.MESSAGE_THREAD_ID.isNull)
            .and(POST.STATE.eq(PostState.PUBLISHED.name))
            .fetchOne(0, Long::class.java)
            ?: 0

    private fun handleUserMessageThreadCounts(post: Post) {
        logger.info(
            "Handling post count for message thread ${post.messageThreadId}.",
            mapOf("userId" to post.userId, "messageThreadId" to post.messageThreadId),
        )

        val postCount = context
            .selectCount()
            .from(POST)
            .where(POST.MESSAGE_THREAD_ID.eq(post.messageThreadId))
            .and(POST.STATE.eq(PostState.PUBLISHED.name))
            .fetchOne(0, Long::class.java)
            ?: 0

        val messageThreadReference = messageThreadsCollection[post.messageThreadId!!]
        val messageThread = messageThreadReference.get()
        val newMessageThread = messageThread
            .copy(posts = postCount)
            .let {
                if (it.lastMessageAt == null || it.lastMessageAt!! < post.created) {
                    it.copy(
                        lastMessageAt = post.created,
                        lastMessageBy = post.userId,
                        lastMessageId = post.id,
                        emailNotified = false,
                    )
                } else {
                    it
                }
            }
            .let {
                if ((it.seens[post.userId] ?: instantMin) < post.created) {
                    it.copy(
                        seens = it.seens.plus(post.userId to post.created),
                    )
                } else {
                    it
                }
            }
            .let {
                if ((it.checks[post.userId] ?: instantMin) < post.created) {
                    it.copy(
                        checks = it.checks.plus(post.userId to post.created),
                    )
                } else {
                    it
                }
            }
        messageThreadReference.set(newMessageThread)
    }
}
