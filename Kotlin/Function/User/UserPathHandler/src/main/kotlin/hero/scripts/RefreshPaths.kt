package hero.scripts

import hero.baseutils.SystemEnv
import hero.functions.UserPathHandler
import hero.gcloud.fetchAll
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.model.User
import hero.model.UserStateChange
import hero.model.UserStateChanged

fun main() {
    val production = true
    val firestore = firestore(SystemEnv.cloudProject, production)
    val handler = UserPathHandler(firestore)
    firestore.typedCollectionOf(User)
        .fetchAll()
        .forEach {
            handler.consume(UserStateChanged(UserStateChange.PATCHED, it))
        }
}
