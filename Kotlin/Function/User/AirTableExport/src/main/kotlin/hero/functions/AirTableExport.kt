package hero.functions

import dev.fuxing.airtable.AirtableApi
import dev.fuxing.airtable.AirtableApi.Table
import dev.fuxing.airtable.AirtableRecord
import dev.fuxing.airtable.exceptions.AirtableApiException
import dev.fuxing.airtable.formula.AirtableFormula
import dev.fuxing.airtable.formula.LogicalOperator
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.baseutils.retryOn
import hero.baseutils.systemEnv
import hero.gcloud.FirestoreRef
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.model.Tier
import hero.model.User
import hero.model.UserStatus
import hero.model.restoreEmail
import java.io.IOException
import java.time.ZoneOffset
import kotlin.reflect.full.memberProperties

@Suppress("unused")
class AirTableExport(
    isProduction: Boolean = SystemEnv.isProduction,
) : FirestoreEventSubcriber() {
    private val firestore: FirestoreRef = firestore(SystemEnv.cloudProject, isProduction)
    private val usersCollection = firestore.typedCollectionOf(User)

    // tokens belong to their creators (originally created by @malinda) and cannot be account-wide
    // see: https://airtable.com/create/tokens
    private val airTableToken: String = systemEnv("AIRTABLE_TOKEN")
    private val airTableApp: String = "appNV60CHWfKxQ1tt"
    private val airTableBase: AirtableApi.Application = AirtableApi(airTableToken).base(airTableApp)

    private val airTableUserTableId: String = "tblqJw2f8Yp1PYe6y"
    private val airTableUserTable: Table = airTableBase.table(airTableUserTableId)

    private val airTableDeletedTableId: String = "tblD7Yxq3sduEGIP7"
    private val airTableDeletedTable: Table = airTableBase.table(airTableDeletedTableId)

    private val notFoundContains: List<String> = listOf(
        "not_found",
        "record not found",
        "model not found",
        "model was not found",
    )
    private val retryMessage: String = "it is safe to retry the request"

    override fun consume(event: FirestoreEvent) {
        exportUser(event.documentId)
    }

    internal fun exportUser(userId: String) {
        val user = usersCollection[userId].fetch()

        // if the user is null, this will remove all its records from airtable as well
        removeDuplicates(userId, user?.airTableId)

        // and if the user is null, we are done
        if (user == null) {
            return
        }

        when (user.status) {
            UserStatus.DELETED -> handleDeletedUser(user)
            else -> handleActiveUser(user)
        }
    }

    private fun handleDeletedUser(user: User) {
        if (user.deletedReason != null) {
            handleForciblyDeletedUser(user)
        }
        if (user.airTableId == null) {
            return
        }
        log.info("Deleting AirTable record ${user.airTableId} for User ${user.id}.", mapOf("userId" to user.id))
        try {
            airTableUserTable.delete(user.airTableId)
        } catch (e: AirtableApiException) {
            handleException(user.id, user.airTableId, e)
        } finally {
            usersCollection[user.id].field(User::airTableId).update(null)
        }
    }

    private fun handleForciblyDeletedUser(user: User) {
        log.info(
            "Refreshing deleted User ${user.id} (deletedAirTableId: ${user.deletedAirTableId}) in Airtable.",
            mapOf("userId" to user.id),
        )

        val userRecord = DeletedUserRecord(
            creatorId = user.id,
            creatorName = user.name,
            country = user.company?.country ?: "CZ",
            language = user.language,
            email = user.email?.restoreEmail() ?: "–",
            url = "https://herohero.co/${user.path}",
            deletedDate = user.deletedAt?.atZone(ZoneOffset.UTC)?.toLocalDate()?.toString(),
            deletedBy = "https://herohero.co/${user.deletedBy}",
            reason = user.deletedReason?.name,
            note = user.deletedNote,
        )

        val airtableRecord = AirtableRecord()
        for (prop in DeletedUserRecord::class.memberProperties) {
            airtableRecord.putField(prop.name, prop.get(userRecord))
        }
        airtableRecord.id = user.deletedAirTableId

        if (user.deletedAirTableId != null) {
            // we try to patch existing record or continue to re-creation if deleted
            retryOn(IOException::class) {
                try {
                    airTableDeletedTable.patch(airtableRecord)
                } catch (e: AirtableApiException) {
                    handleException(user.id, user.deletedAirTableId, e)
                }
            }
            // we are done, do not create a new record below
            return
        }

        val response = airTableDeletedTable.post(airtableRecord)
        usersCollection[user.id].field(User::deletedAirTableId).update(response.id)
    }

    private fun handleActiveUser(user: User) {
        // non-creators are skipped
        if (user.creator.stripeAccountId == null) {
            return
        }

        log.info(
            "Refreshing User ${user.id} (airTableId: ${user.airTableId}) in Airtable.",
            mapOf("creatorId" to user.id),
        )

        val tier = Tier.ofId(user.creator.tierId)
        val userRecord = UserRecord(
            created = user.created.atZone(ZoneOffset.UTC).toLocalDate().toString(),
            creatorId = user.id,
            creatorName = user.name,
            country = user.company?.country ?: "CZ",
            language = user.language,
            lastPostAt = user.lastPostAt?.atZone(ZoneOffset.UTC)?.toLocalDate()?.toString(),
            bio = user.bio,
            email = user.email ?: "–",
            url = "https://herohero.co/${user.path}",
            subscriberCount = user.counts.supporters,
            subscriberFee = user.counts.incomes ?: 0,
            postCount = user.counts.posts ?: 0,
            active = user.creator.active,
            verified = user.creator.verified,
            tierPrice = tier.priceCents / 100L,
            tierCurrency = tier.currency.name,
            vatId = user.company?.vatId,
            stripeAccountId = user.creator.stripeAccountId,
        )

        val airtableRecord = AirtableRecord()
        for (prop in UserRecord::class.memberProperties) {
            airtableRecord.putField(prop.name, prop.get(userRecord))
        }
        airtableRecord.id = user.airTableId

        if (user.airTableId != null) {
            // we try to patch existing record or continue to re-creation if deleted
            retryOn(IOException::class) {
                try {
                    airTableUserTable.patch(airtableRecord)
                } catch (e: AirtableApiException) {
                    handleException(user.id, user.airTableId, e)
                }
            }
            // we are done, do not create a new record below
            return
        }

        val response = airTableUserTable.post(airtableRecord)
        usersCollection[user.id].field(User::airTableId).update(response.id)
    }

    /**
     * If for some reason this consumer runs twice in parallel, it might happen that one record
     * will be created twice. We should therefore try to delete these trailing records if they exist.
     */
    private fun removeDuplicates(
        userId: String,
        airtableIdToIgnore: String?,
    ) {
        val duplicateIds: List<String> = airTableUserTable
            .list {
                it.filterByFormula(
                    LogicalOperator.EQ,
                    AirtableFormula.Object.field(UserRecord::creatorId.name),
                    AirtableFormula.Object.value(userId),
                )
            }
            .map { it.id!! }
            .minus(airtableIdToIgnore ?: "")

        if (duplicateIds.isNotEmpty()) {
            log.info("Deleting duplicate AirTable records for User $userId: $duplicateIds", mapOf("userId" to userId))
            duplicateIds.forEach { duplicateAirTableId ->
                retryOn(IOException::class) {
                    try {
                        airTableUserTable.delete(duplicateAirTableId)
                    } catch (e: AirtableApiException) {
                        handleException(userId, duplicateAirTableId, e)
                    }
                }
            }
        }
    }

    private fun handleException(
        userId: String?,
        airTableId: String?,
        e: AirtableApiException,
    ) {
        val message = e.message ?: e::class.simpleName!!
        when {
            retryMessage in message ->
                throw IOException(message)
            notFoundContains.any { it in message.lowercase() } ->
                log.debug("User $userId already deleted: $message")
            else ->
                throw IllegalStateException("Cannot handle $userId/$airTableId: $message", e)
        }
    }

    data class UserRecord(
        val created: String,
        val lastPostAt: String?,
        val creatorId: String,
        val creatorName: String,
        val country: String,
        val language: String,
        val bio: String,
        val email: String,
        val url: String,
        val subscriberCount: Long,
        val subscriberFee: Long,
        val postCount: Long,
        val active: Boolean,
        val verified: Boolean,
        val tierPrice: Long,
        val tierCurrency: String,
        val vatId: String?,
        val stripeAccountId: String?,
    )

    data class DeletedUserRecord(
        val creatorId: String,
        val creatorName: String,
        val country: String,
        val language: String,
        val email: String,
        val url: String,
        val deletedDate: String?,
        val deletedBy: String?,
        val reason: String?,
        val note: String?,
    )
}
