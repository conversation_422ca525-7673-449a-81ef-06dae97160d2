package hero.functions

import com.stripe.model.PaymentMethod
import com.stripe.net.RequestOptions
import com.stripe.param.CustomerCreateParams
import com.stripe.param.PaymentMethodCreateParams
import com.stripe.param.PriceCreateParams
import com.stripe.param.PriceCreateParams.Recurring.Interval.DAY
import com.stripe.param.SubscriptionRetrieveParams
import hero.baseutils.SystemEnv
import hero.gcloud.FirestoreRef
import hero.gcloud.firestore
import hero.model.CZ_VAT_COUNTRY
import hero.model.Creator
import hero.model.Currency.EUR
import hero.model.Tier
import hero.model.User
import hero.model.UserCompany
import hero.model.topics.CardCreateType
import hero.model.topics.RefundMethod
import hero.stripe.service.StripeClients
import hero.stripe.service.StripeCouponService
import hero.stripe.service.StripePaymentMethodsService
import hero.stripe.service.StripeService
import hero.stripe.service.StripeSubscriptionService
import hero.stripe.service.VatMapping
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import java.time.Instant
import kotlin.test.assertEquals

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
internal class StripeCancelAndRefundIT {
    private val isProduction = false
    private val firestore: FirestoreRef = firestore(SystemEnv.cloudProject, isProduction)
    private val testTier = Tier("EUR05", 500, true, EUR, false)

    // generated byt test/hero.stripe.service.Helpers.kt
    private val testingAccountId: String = "acct_1NuwgiPnHwXE6FBD"
    private val subRetrieveParams = SubscriptionRetrieveParams.builder()
        .addAllExpand(
            listOf(
                "latest_invoice.payment_intent",
                "latest_invoice.payment_intent.latest_charge",
                "latest_invoice.payment_intent.latest_charge.refunds",
            ),
        )
        .build()

    private val stripeClients = StripeClients(SystemEnv.stripeKeyEu, SystemEnv.stripeKeyUs)
    private val stripeService = StripeService(stripeClients, null)
    private val stripePaymentMethodsService = StripePaymentMethodsService(stripeClients, stripeService, null)
    private val deletedUserHandler = DeletedUserHandler(isProduction, firestore)

    private val subscriptionService = StripeSubscriptionService(
        clients = stripeClients,
        paymentMethodsService = stripePaymentMethodsService,
        production = isProduction,
        countryToVatMapping = VatMapping(mapOf()),
        recurringPeriod = PriceCreateParams.Recurring.Interval.MONTH,
        stripeCouponService = StripeCouponService(stripeClients),
    )

    private val customer1 = stripeClients[EUR].customers().create(
        CustomerCreateParams
            .builder()
            .setEmail("<EMAIL>")
            .setName("Herohero testing account")
            .build(),
    )

    private val price = stripeClients[EUR].prices().create(
        PriceCreateParams
            .builder()
            .setCurrency("EUR")
            .setProductData(PriceCreateParams.ProductData.builder().setName("Testing product").build())
            .setUnitAmount(100L)
            .setRecurring(subscriptionService.createRecurringPeriod(DAY))
            .build(),
    )

    private val paymentMethod1 = paymentMethod(customerId = customer1.id)

    @Test
    @Disabled(
        """
        TODO: https://linear.app/herohero/issue/HH-708/rewrite-stripecancelandrefundit
        This test must be disabled by default (and run manually) as Stripe does not process
        subscriptions in timely fashion and the test will most probably not finish on time.

        Also this part has been migrated into a Cloudfunction, so this test might need to be
        completely rewritten, but not sure how.
        """,
    )
    fun `cancel all creators' subscriptions without refund`() {
        val testCreatorId = "test-creator-${System.currentTimeMillis()}"
        val testUserId = "test-user-${System.currentTimeMillis()}"
        val testCreator = User(
            id = testCreatorId,
            company = UserCompany(country = "CZ"),
            path = "test",
            name = "test",
            email = "<EMAIL>",
            creator = Creator(tierId = "EUR05"),
        )
        val connectedAccount = stripeClients[EUR].accounts().retrieve(testingAccountId)

        var sub1 = subscriptionService.createSubscription(
            customerId = customer1.id,
            tier = testTier,
            priceId = price.id,
            creatorId = testCreatorId,
            userId = testUserId,
            paymentMethodId = paymentMethod1.id,
            couponId = null,
            creatorStripeAccountId = connectedAccount.id,
            creatorCountry = CZ_VAT_COUNTRY,
            creatorVatId = null,
            subscribed = Instant.now(),
            isResubscription = false,
            cardCreateType = null,
            currency = testTier.currency,
        )

        var sub2 = subscriptionService.createSubscription(
            customerId = customer1.id,
            tier = testTier,
            priceId = price.id,
            creatorId = testCreatorId,
            userId = testUserId,
            paymentMethodId = paymentMethod1.id,
            couponId = null,
            creatorStripeAccountId = connectedAccount.id,
            creatorCountry = CZ_VAT_COUNTRY,
            creatorVatId = null,
            subscribed = Instant.now(),
            isResubscription = false,
            cardCreateType = null,
            currency = testTier.currency,
        )

        var retries = 0
        while (true) {
            Thread.sleep(retries * 1000L)
            // we must wait until both subscriptions are available
            val subs = subscriptionService.getSubscriptionsByCreator(testCreatorId, filterActive = true, EUR)
            if (subs.toList().size == 2) {
                break
            }
            retries++
            if (retries > 30) {
                error("Subscriptions didn't become available on time.")
            }
        }

        deletedUserHandler.cancelSubscriptionsOfCreator(
            creator = testCreator,
            atPeriodEnd = false,
            cancelledBy = "cancelling-user",
            refundMethod = RefundMethod.NONE,
            currency = EUR,
            labels = mapOf(),
        )

        retries = 0
        // we must wait until both subscriptions are properly cancelled internally
        while (true) {
            Thread.sleep(retries * 1000L)
            sub1 = stripeClients[EUR].subscriptions().retrieve(sub1.id, subRetrieveParams, RequestOptions.getDefault())
            sub2 = stripeClients[EUR].subscriptions().retrieve(sub2.id, subRetrieveParams, RequestOptions.getDefault())
            if (sub1.status == "canceled" && sub2.status == "canceled") {
                break
            }
            retries++
            if (retries > 30) {
                error("Subscriptions didn't become available on time.")
            }
        }

        sub1 = stripeClients[EUR].subscriptions().retrieve(sub1.id, subRetrieveParams, RequestOptions.getDefault())
        sub2 = stripeClients[EUR].subscriptions().retrieve(sub2.id, subRetrieveParams, RequestOptions.getDefault())
        assertEquals("canceled", sub1.status)
        assertEquals("canceled", sub2.status)
        // refund was not performed
        assertEquals(false, sub1.latestInvoiceObject.paymentIntentObject.latestChargeObject.refunded)
        assertEquals(false, sub2.latestInvoiceObject.paymentIntentObject.latestChargeObject.refunded)
    }

    @Test
    @Disabled(
        """
        TODO: https://linear.app/herohero/issue/HH-708/rewrite-stripecancelandrefundit
        This test must be disabled by default (and run manually) as Stripe does not process
        subscriptions in timely fashion and the test will most probably not finish on time.

        Also this part has been migrated into a Cloudfunction, so this test might need to be
        completely rewritten, but not sure how.
        """,
    )
    fun `cancel all creators' subscriptions with refund`() {
        val testCreatorId = "test-creator-${System.currentTimeMillis()}"
        val testUserId = "test-user-${System.currentTimeMillis()}"
        val testCreator = User(
            id = testCreatorId,
            company = UserCompany(country = "CZ"),
            path = "test",
            name = "test",
            creator = Creator(tierId = "EUR05"),
        )
        val connectedAccount = stripeClients[EUR].accounts().retrieve(testingAccountId)

        var sub1 = subscriptionService.createSubscription(
            customerId = customer1.id,
            tier = testTier,
            priceId = price.id,
            creatorId = testCreatorId,
            userId = testUserId,
            paymentMethodId = paymentMethod1.id,
            couponId = null,
            creatorStripeAccountId = connectedAccount.id,
            creatorCountry = CZ_VAT_COUNTRY,
            creatorVatId = null,
            subscribed = Instant.now(),
            isResubscription = false,
            cardCreateType = null,
            currency = EUR,
        )

        var sub2 = subscriptionService.createSubscription(
            customerId = customer1.id,
            tier = testTier,
            priceId = price.id,
            creatorId = testCreatorId,
            userId = testUserId,
            paymentMethodId = paymentMethod1.id,
            couponId = null,
            creatorStripeAccountId = connectedAccount.id,
            creatorCountry = CZ_VAT_COUNTRY,
            creatorVatId = null,
            subscribed = Instant.now(),
            isResubscription = false,
            cardCreateType = null,
            currency = EUR,
        )

        var retries = 0
        // we must wait until both subscriptions are available
        while (true) {
            Thread.sleep(retries * 1000L)
            val subs = subscriptionService.getSubscriptionsByCreator(testCreatorId, true, EUR)
            if (subs.toList().size == 2) {
                break
            }
            retries++
            if (retries > 30) {
                error("Subscriptions didn't become available on time: ${subs.toList()}")
            }
        }

        deletedUserHandler.cancelSubscriptionsOfCreator(
            testCreator,
            false,
            "cancelling-user",
            RefundMethod.REFUND,
            EUR,
            mapOf(),
        )

        retries = 0
        // we must wait until both subscriptions are properly cancelled internally
        while (true) {
            Thread.sleep(retries * 1000L)
            sub1 = stripeClients[EUR].subscriptions().retrieve(sub1.id, subRetrieveParams, RequestOptions.getDefault())
            sub2 = stripeClients[EUR].subscriptions().retrieve(sub2.id, subRetrieveParams, RequestOptions.getDefault())
            if (sub1.status == "canceled" && sub2.status == "canceled") {
                break
            }
            retries++
            if (retries > 30) {
                error("Subscriptions didn't become available on time: ${sub1.status}/${sub2.status}")
            }
        }

        assertEquals("canceled", sub1.status)
        assertEquals("canceled", sub2.status)
        // refund was not performed
        val charge1 = sub1.latestInvoiceObject.paymentIntentObject.latestChargeObject
        val charge2 = sub2.latestInvoiceObject.paymentIntentObject.latestChargeObject
        assertEquals(true, charge1.refunded)
        assertEquals(true, charge2.refunded)
        val refunds1 = charge1.refunds.data
        val refunds2 = charge2.refunds.data

        assertEquals(1, refunds1.size)
        assertEquals(1, refunds2.size)

        assertEquals(charge1.amount, refunds1.first().amount)
        assertEquals(charge2.amount, refunds2.first().amount)

        val transfer1 = stripeClients[EUR].transfers().retrieve(charge1.transfer)
        val transfer2 = stripeClients[EUR].transfers().retrieve(charge2.transfer)

        assertEquals(connectedAccount.id, transfer1.destination)
        assertEquals(connectedAccount.id, transfer2.destination)

        val reversal1 = transfer1.reversals.retrieve(refunds1.first().transferReversal)
        val reversal2 = transfer2.reversals.retrieve(refunds2.first().transferReversal)

        assertEquals(transfer1.amount, reversal1.amount)
        assertEquals(transfer2.amount, reversal2.amount)

        assertEquals(transfer1.id, reversal1.transfer)
        assertEquals(transfer2.id, reversal2.transfer)
    }

    private fun paymentMethod(customerId: String): PaymentMethod {
        val paymentMethod = stripeClients[EUR].paymentMethods().create(
            PaymentMethodCreateParams
                .builder()
                .setType(PaymentMethodCreateParams.Type.CARD)
                .setCard(
                    PaymentMethodCreateParams.Token.builder()
                        .setToken("tok_visa")
                        .build(),
                )
                .build(),
        )
        // we associate the new invalid payment method with the customer
        stripePaymentMethodsService.putPaymentMethodViaSetupIntent(
            paymentMethodId = paymentMethod.id,
            customerId = customerId,
            cardCreateType = CardCreateType.CARD,
            makeDefault = true,
            currency = EUR,
        )

        return paymentMethod
    }
}
