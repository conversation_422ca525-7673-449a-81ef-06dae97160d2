package hero.functions

import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.gcloud.PubSub
import hero.model.UserStateChange
import hero.model.UserStateChanged
import hero.model.topics.EmailPublished

@Suppress("Unused")
class NewUserWelcomer(
    private val pubSub: PubSub = PubSub(SystemEnv.environment, SystemEnv.cloudProject),
) : PubSubSubscriber<UserStateChanged>() {
    override fun consume(payload: UserStateChanged) {
        val user = payload.user
        if (payload.stateChange != UserStateChange.CREATED) {
            log.debug("State change is not CREATED, skipping: ${payload.stateChange}.", mapOf("userId" to user.id))
            return
        }
        if (user.email == null) {
            log.warn("User does not have email set.", mapOf("userId" to user.id))
            return
        }
        log.info("Welcoming new user ${user.id}", mapOf("userId" to user.id))

        pubSub.publish(
            EmailPublished(
                to = user.email!!,
                // https://gitlab.com/-/ide/project/heroheroco/mailgun-templates/tree/main/-/templates/
                template = "welcome-new-user",
                variables = listOf(
                    "user-name" to user.name,
                ),
                language = user.language,
            ),
        )
    }
}
