package hero.stripe.service

import com.stripe.exception.InvalidRequestException
import com.stripe.model.Coupon
import com.stripe.model.Price
import com.stripe.param.CouponCreateParams
import com.stripe.param.CouponCreateParams.Duration
import hero.baseutils.randomString
import hero.model.Currency
import hero.model.Tier
import java.time.Instant
import hero.model.CouponMethod as CM
import hero.model.CouponTarget as CT

class StripeCouponService(private var clients: StripeClients) {
    /** @returns non-prefixed "code" used in UI */
    fun generateCouponCode(
        creatorId: String,
        currency: Currency,
    ): String {
        while (true) {
            val couponId = randomString(10).uppercase()
            // check if coupon with given id exists and return its value if not
            getCouponOrNull("$creatorId-$couponId", currency) ?: return couponId
        }
    }

    fun createCoupon(
        couponCode: String,
        purchasedByUserId: String,
        creatorId: String?,
        tier: Tier?,
        price: Price?,
        percentOff: Int?,
        currency: Currency,
        months: Int?,
        days: Int?,
        campaign: String,
        redeemBy: Instant? = null,
        redemptions: Int = 1,
        extraMetadata: Map<String, String> = mapOf(),
    ): Coupon =
        retry {
            if (days != null && percentOff != 100) {
                error("When creating n-day trials, the percentOff must be 100.")
            }
            if (creatorId == null) {
                // https://gitlab.com/heroheroco/backend/-/merge_requests/542
                throw NotImplementedError("We don't support general Herohero vouchers yet.")
            }
            if (months == null && days == null && (tier != null || percentOff == null)) {
                error("For paid coupons, `months` or `days` must always be given.")
            }
            clients[currency].coupons().create(
                CouponCreateParams.builder()
                    .setId("$creatorId-$couponCode")
                    .setDuration(
                        when {
                            months != null && days != null -> error("Only one of `months` or `days` must be given.")
                            months != null -> Duration.REPEATING
                            days != null -> Duration.ONCE
                            else -> Duration.FOREVER
                        },
                    )
                    .setDurationInMonths(months?.toLong())
                    .setMaxRedemptions(redemptions.toLong())
                    .setRedeemBy(redeemBy?.epochSecond)
                    .let {
                        if (price != null) {
                            it.setAppliesTo(
                                CouponCreateParams.AppliesTo.builder()
                                    .addProduct(price.product)
                                    .build(),
                            )
                        } else {
                            it
                        }
                    }
                    .let {
                        if (days != null) {
                            it.setPercentOff("0.01".toBigDecimal())
                        } else if (percentOff != null) {
                            it.setPercentOff(percentOff.toBigDecimal())
                        } else {
                            if (tier == null) {
                                error("Tier must be given if percentOff is null.")
                            }
                            // this is not a total sum, but monthly fee off
                            it.setAmountOff(tier.priceCents.toLong())
                                .setCurrency(tier.currency.name.lowercase())
                        }
                    }
                    .setMetadata(
                        mapOf(
                            "purchasedByUserId" to purchasedByUserId,
                            "couponCode" to couponCode,
                            // @DEPRECATED use couponTarget
                            "couponType" to if (creatorId != null) CT.CREATOR.name else CT.HEROHERO.name,
                            "couponTarget" to if (creatorId != null) CT.CREATOR.name else CT.HEROHERO.name,
                            "couponMethod" to if (percentOff != null) CM.TRIAL.name else CM.VOUCHER.name,
                            "creatorId" to creatorId,
                            "tierId" to tier?.id,
                            "months" to months?.toString(),
                            // https://linear.app/herohero/issue/HH-1918/1-week-long-trials
                            "days" to days?.toString(),
                            "campaign" to campaign,
                        ) + extraMetadata,
                    )
                    .build(),
            )
        }

    fun getCouponOrNull(
        userId: String,
        couponCode: String,
        currency: Currency,
    ): Coupon? =
        getCouponOrNull("$userId-$couponCode", currency)
            // or fallback to legacy version without userId prefix
            ?: getCouponOrNull(couponCode, currency)

    fun getCouponOrNull(
        couponId: String,
        currency: Currency,
    ): Coupon? =
        stripeRetry {
            try {
                clients[currency].coupons().retrieve(couponId)
            } catch (e: InvalidRequestException) {
                if ("No such coupon" in e.message!!) {
                    null
                } else {
                    throw e
                }
            }
        }
}
